{"name": "doc-app-mono", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "turbo test", "dev": "turbo dev", "prod": "turbo prod", "build": "turbo build", "db": "turbo docker:start:db", "checks": "turbo checks", "precommit": "turbo precommit", "prepare": "husky"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"husky": "^9.1.7", "turbo": "^2.1.3"}, "packageManager": "pnpm@8.15.6", "engines": {"node": ">=18"}, "dependencies": {"http-status": "^2.1.0", "papaparse": "^5.5.2"}}