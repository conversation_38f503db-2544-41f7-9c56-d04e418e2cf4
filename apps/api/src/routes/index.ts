import express from 'express';
import docRoutes from './doc.route';
import zohoRoutes from './zoho.route';
import funnelRoutes from './funnel.route';
import chatRoutes from './chat.route';
import dailyRoutes from './daily.route';
import moderationRoutes from './moderation.route';
import reportsRoutes from './reports.route';
import availabilityRoutes from './availability.route';
import requestsRoutes from './requests.route';
import adminRequestsRoutes from './admin-requests.route';
import adminQuestionnaireRoutes from './admin-questionnaire.route';


const router = express.Router();

const routes = [
  {
    path: '/doc',
    route: docRoutes,
  },
  {
    path: '/zoho',
    route: zohoRoutes,
  },
  {
    path: '/funnel',
    route: funnelRoutes,
  },
  {
    path: '/chat',
    route: chatRoutes,
  },
  {
    path: '/daily',
    route: dailyRoutes,
  },
  {
    path: '/moderation',
    route: moderationRoutes,
  },
  {
    path: '/report',
    route: reportsRoutes,
  },
  {
    path: '/availability',
    route: availabilityRoutes,
  },
  {
    path: '/requests',
    route: requestsRoutes,
  },
  {
    path: '/admin',
    route: adminRequestsRoutes,
  },
  {
    path: '/admin-questionnaire',
    route: adminQuestionnaireRoutes,
  },

];

routes.forEach((route) => {
  router.use(route.path, route.route);
});
export default router;
