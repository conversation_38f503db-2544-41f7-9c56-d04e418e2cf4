import { postPatientBooking, getPatientBookingHistory, getAdminBookings, getContactDetailsByZohoId, getContactIdByEmail, getLeadByEmail, updateLastDoctorMessageField, getTreatmentPlanByEmail, createPatientFromZohoContact,getPatientBookings } from '../controllers/zoho';
import {
    customerCallHandler,

    create<PERSON>eads,
    callStatusHandler,
    startAutodialer,
    amdStatusHandler,
    updateZohoLeadStatusHandler,
    getStatus,
    callCompletedHandler,
    agentCallHandler,
    handleCustomerDialStatus,
} from '../controllers/autodialer';

import express from 'express';

const router = express.Router();

const currentVersion = 'v1.0';

router.post(`/${currentVersion}/booking/:leadId/:flag`, postPatientBooking);
router.get(`/${currentVersion}/booking-history/:patientId`, getPatientBookingHistory);
router.get(`/${currentVersion}/admin-bookings`, getAdminBookings);

// Patient booking management endpoints
router.get(`/${currentVersion}/patient/:email/bookings`, getPatientBookings);

router.post('/customer-call-handler', customerCallHandler);
router.post('/call-completed', callCompletedHandler);
router.post('/create-leads', createLeads);
router.post('/agent-call-handler',agentCallHandler);
router.post('/call-status-handler', callStatusHandler);
router.post('/start-auto-dialer', startAutodialer);
router.post('/customer-dial-status', handleCustomerDialStatus);
router.post('/amd-status-handler', amdStatusHandler);
router.post('/update-status', updateZohoLeadStatusHandler);
router.get('/status', getStatus);
router.post(`/${currentVersion}/last-doctor-message`, updateLastDoctorMessageField);
router.get(`/${currentVersion}/contacts/by-email`, getContactIdByEmail);
router.get(`/${currentVersion}/treatment-plan/by-email`, getTreatmentPlanByEmail);
router.get(`/${currentVersion}/contacts/:zohoId`, getContactDetailsByZohoId);
router.post(`/${currentVersion}/contacts/:zohoId/create-patient`, createPatientFromZohoContact);
router.get(`/${currentVersion}/leads/by-email`, getLeadByEmail);

export default router;
