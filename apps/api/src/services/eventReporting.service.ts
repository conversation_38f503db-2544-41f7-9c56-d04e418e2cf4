import { WebClient } from '@slack/web-api';
import config from '../config';
import { logger } from '../config/logger';
import { DateTime } from 'luxon';

export interface UserEvent {
  actor: 'Doctor' | 'Patient';
  target: string;
  action: string;
  message: string;
  timestamp: string;
  actor_id: string;
  target_id?: string;
  doctor_name: string; // Added to include doctor's name in the report
}

const textToMarkRed = [
  'Treatment plan was cancelled by doctor',
  'submitted diagnosis with cancelled treatment plan',
  'auto redirect to online patient after submitting Treatment Plan. No available patient',
  'Redirected to next patient from email submission',
  'submitted diagnosis with treatment plan',
  "Doctor was redirected because of patient's technical issues",
  'auto redirect to online patient after they left the call due to technical issues. No available patient',
  'The Patient treatment plan was rejected',
  'clicked the confirm button',
  'Submitting Default Strains',
  'Redirected to next patient from email submission',
  'Redirected to next patient from email cancellation',
  'returned to email screen',
  'left email screen',
  'auto confirmed treatment plan for',
  'Patient has been sent an email from',
];

const endMarkers = 'Patient joined the call with Doctor';

class EventReportingService {
  private slack: WebClient;

  constructor() {
    this.slack = new WebClient(config.slackEventToken);
  }

  async reportEvent(events: UserEvent[]): Promise<string | null> {
    const slackChannel = config.slackEventReportingChannel;
    if (!slackChannel || !config.slackEventToken) {
      logger.info('No Slack channel or token configured for event reporting');
      return null;
    }

    const sortedEvents = [...events].sort((a, b) => Date.parse(a.timestamp) - Date.parse(b.timestamp));

    // const eventLines = sortedEvents
    //   .map((e, idx) => {
    //     const isRedText = textToMarkRed.some(text => e.message.includes(text));
    //     const prefix = isRedText ? ':red_circle:' : '';
    //     const targetInfo = e.target ? ` Target: ${e.target}` : e.target_id ? ` Target: ${e.target_id}` : '';
    //     return `${prefix} ${idx + 1}. [${DateTime.fromISO(e.timestamp).toFormat('h:mm:ss a, dd MMM yyyy')}] ${e.message} | ${targetInfo}`;
    //   })
    //   .join('\n');
    const eventLines: string[] = [];
    const batchIndices: number[] = [];
    let whiteCircle = false;
    let lastWhiteIdx = -1;
    let addedWhiteCircle = false;

    for (let idx = 0; idx < sortedEvents.length; idx++) {
      const e = sortedEvents[idx];
      const isRedText = textToMarkRed.some((text) => e.message.includes(text));
      const isEndMarker = e.message.includes(endMarkers);
      const prefix = isRedText ? ':red_circle:' : '';
      const targetInfo = e.target ? `| Target: ${e.target}` : e.target_id ? `| Target: ${e.target_id}` : '';

      if (isEndMarker) {
        whiteCircle = false;
        addedWhiteCircle = false;
        if (lastWhiteIdx !== -1) batchIndices.push(idx);
      }
      if (isRedText) whiteCircle = true;

      let line = `${prefix} ${idx + 1}. [${DateTime.fromISO(e.timestamp).toFormat('h:mm:ss a, dd MMM yyyy')}] ${e.message} ${targetInfo}\n`;
      if (whiteCircle && !isRedText) {
        lastWhiteIdx = idx;
        if (!addedWhiteCircle) {
          batchIndices.push(idx);
          addedWhiteCircle = true;
        }
        line = `:white_circle:${line}`;
      }

      eventLines.push(line);
    }
    logger.info('Event length: ' + eventLines.length);
    logger.info('Batch Indices: ' + batchIndices);

    for (let i = 0; i < batchIndices.length; i++) {
      const start = i === 0 ? 0 : batchIndices[i - 1];
      const end = batchIndices[i];
      const batchLines = eventLines.slice(start, end).join('');
      // Process the batch as needed

      const messageText = `
      🔔 *Doctor Activity Report*
      *Doctor*: ${sortedEvents[0].doctor_name}

      -------------------------------------------------------------------------
      *Timeline of Actions:* ${i > 0 && (i - 1) % 2 === 0 ? 'Between Consultations' : 'Consultation'}
      -------------------------------------------------------------------------\n
      ${batchLines}
    `
        .trim()
        .replace(/,/g, '');

      try {
        const response = await this.slack.chat.postMessage({
          channel: slackChannel,
          text: messageText,
          username: 'Event Reporter',
          icon_emoji: ':robot_face:',
        });

        logger.info(`Event report sent to Slack channel ${slackChannel}: ${response.ts}`);
      } catch (error) {
        logger.error('Error reporting event:', error);
      }
    }

    if (batchIndices.length > 0 && batchIndices[batchIndices.length - 1] < eventLines.length) {
      const lastBatch = eventLines.slice(batchIndices[batchIndices.length - 1]);
      const messageText = `
      🔔 *Doctor Activity Report*
      *Doctor*: ${sortedEvents[0].doctor_name}

      -------------------------------------------------------------------------
      *Timeline of Actions:* Consultation
      -------------------------------------------------------------------------\n
      ${lastBatch.join('')}
    `
        .trim()
        .replace(/,/g, '');

      try {
        const response = await this.slack.chat.postMessage({
          channel: slackChannel,
          text: messageText,
          username: 'Event Reporter',
          icon_emoji: ':robot_face:',
        });

        logger.info(`Event report sent to Slack channel ${slackChannel}: ${response.ts}`);
      } catch (error) {
        logger.error('Error reporting event:', error);
      }
    }

    return null; // Return null if no events were reported
  }

  async sendTestNotification(): Promise<void> {
    const slackChannel = config.slackEventReportingChannel;

    if (!slackChannel || !config.slackEventToken) {
      throw new Error('Slack event reporting channel or token not configured');
    }

    const now = DateTime.now().setZone('Australia/Sydney');
    const timestamp = now.toFormat('h:mm a, dd MMM yyyy');

    const messageText = `🧪 *TEST EVENT REPORTING NOTIFICATION*
        This is a test notification sent at ${timestamp}
    `;

    try {
      await this.slack.chat.postMessage({
        channel: slackChannel,
        text: messageText,
        username: 'Event Reporter',
        icon_emoji: ':robot_face:',
      });
    } catch (error) {
      logger.error('Error sending test notification:', error);
    }
  }
}

export const eventReportingService = new EventReportingService();
