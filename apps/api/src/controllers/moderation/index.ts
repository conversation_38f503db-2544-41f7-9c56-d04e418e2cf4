import { Request<PERSON>and<PERSON> } from 'express-serve-static-core';
import { catchAll } from '../../utils/catchAll';
import { ApiError } from '../../utils/ApiError';
import httpStatus from 'http-status';
import moderationService from '../../services/moderation.service';
import { getCookie } from '../../utils/cookie';
import { db } from '../../utils/db';
import { WebSocketManager } from '../../helpers/webSocketManager';

/**
 * Get pending conversations for moderation
 * @route GET /moderation/pending
 */
export const getPendingConversations: RequestHandler = catchAll(async (req, res) => {
  const limit = parseInt(req.query.limit as string) || 5;
  const page = parseInt(req.query.page as string) || 1;

  const result = await moderationService.getPendingConversations(limit, page);

  res.status(200).json({
    success: true,
    conversations: result.conversations,
    pagination: result.pagination
  });
});

/**
 * Get pending messages for moderation (legacy endpoint)
 * @route GET /moderation/pending-messages
 */
export const getPendingMessages: RequestHandler = catchAll(async (req, res) => {
  const limit = parseInt(req.query.limit as string) || 50;
  const page = parseInt(req.query.page as string) || 1;
  const offset = (page - 1) * limit;

  const client = await db.connect();

  try {
    // Get total count for pagination metadata
    const countQuery = `
      SELECT COUNT(*) as total
      FROM chat_conversation_moderation
      WHERE "moderationStatus" = 'pending'
    `;

    // Get paginated conversations with enriched data
    const conversationsQuery = `
      SELECT
        ccm.*,
        tp.outcome as "treatmentOutcome",
        dr.name as "doctorName"
      FROM chat_conversation_moderation ccm
      LEFT JOIN treatmentplan tp ON ccm."treatmentPlanId" = tp.id
      LEFT JOIN Dr dr ON tp."drId" = dr."accessID"
      WHERE ccm."moderationStatus" = 'pending'
      ORDER BY ccm."createdAt" DESC
      LIMIT $1 OFFSET $2
    `;

    const [countResult, conversationsResult] = await Promise.all([
      client.query(countQuery),
      client.query(conversationsQuery, [limit, offset])
    ]);

    const total = parseInt(countResult.rows[0]?.total || '0');
    const totalPages = Math.ceil(total / limit);

    const response = {
      success: true,
      conversations: conversationsResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };

    console.log('getPendingConversations response:', JSON.stringify(response, null, 2));
    res.status(200).json(response);
  } finally {
    client.release();
  }
});

/**
 * Get moderated messages (approved/rejected)
 * @route GET /moderation/moderated
 */
export const getModeratedMessages: RequestHandler = catchAll(async (req, res) => {
  const limit = parseInt(req.query.limit as string) || 50;
  const page = parseInt(req.query.page as string) || 1;
  const offset = (page - 1) * limit;
  const status = req.query.status as string; // 'approved', 'rejected', or undefined for both

  const client = await db.connect();

  try {
    // Build count query for conversations
    let countQuery = `
      SELECT COUNT(*) as total
      FROM chat_conversation_moderation
      WHERE "moderationStatus" IN ('approved', 'rejected')
    `;

    // Build conversations query with enriched data
    let conversationsQuery = `
      SELECT
        ccm.*,
        COALESCE(dr.username, dr.name, ccm."moderatedBy") as "moderatorName",
        tp.outcome as "treatmentOutcome",
        doc.name as "doctorName"
      FROM chat_conversation_moderation ccm
      LEFT JOIN Dr dr ON ccm."moderatedBy" = dr."accessID"
      LEFT JOIN treatmentplan tp ON ccm."treatmentPlanId" = tp.id
      LEFT JOIN Dr doc ON tp."drId" = doc."accessID"
      WHERE ccm."moderationStatus" IN ('approved', 'rejected')
    `;

    const countParams: (string | number)[] = [];
    const conversationParams: (string | number)[] = [];

    if (status && ['approved', 'rejected'].includes(status)) {
      countQuery += ` AND "moderationStatus" = $${countParams.length + 1}`;
      countParams.push(status);

      conversationsQuery += ` AND ccm."moderationStatus" = $${conversationParams.length + 1}`;
      conversationParams.push(status);
    }

    conversationsQuery += ` ORDER BY ccm."moderatedAt" DESC LIMIT $${conversationParams.length + 1} OFFSET $${conversationParams.length + 2}`;
    conversationParams.push(limit, offset);

    const [countResult, conversationsResult] = await Promise.all([
      client.query(countQuery, countParams),
      client.query(conversationsQuery, conversationParams)
    ]);

    const total = parseInt(countResult.rows[0]?.total || '0');
    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      success: true,
      data: conversationsResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } finally {
    client.release();
  }
});

/**
 * Get moderation statistics
 * @route GET /moderation/stats
 */
export const getModerationStats: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();

  try {
    // Get counts by status for conversations
    const statsQuery = `
      SELECT
        "moderationStatus",
        COUNT(*) as count
      FROM chat_conversation_moderation
      GROUP BY "moderationStatus"
    `;

    const todayQuery = `
      SELECT COUNT(*) as today_count
      FROM chat_conversation_moderation
      WHERE "createdAt" >= CURRENT_DATE AT TIME ZONE 'Australia/Sydney'
    `;

    const [statsResult, todayResult] = await Promise.all([
      client.query(statsQuery),
      client.query(todayQuery)
    ]);

    const stats = {
      pending: 0,
      approved: 0,
      rejected: 0,
      todayTotal: parseInt(todayResult.rows[0]?.today_count || '0')
    };

    statsResult.rows.forEach(row => {
      stats[row.moderationStatus as keyof typeof stats] = parseInt(row.count);
    });

    // Calculate total
    const total = stats.pending + stats.approved + stats.rejected;

    res.status(200).json({
      success: true,
      data: {
        ...stats,
        total
      }
    });
  } finally {
    client.release();
  }
});

/**
 * Moderate a conversation (approve or reject)
 * @route POST /moderation/moderate-conversation
 */
export const moderateConversation: RequestHandler = catchAll(async (req, res) => {
  const { channelId, action, reason } = req.body;

  if (!channelId || !action) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Channel ID and action are required');
  }

  if (!['approve', 'reject'].includes(action)) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Action must be "approve" or "reject"');
  }

  // Get moderator ID from cookie or use system default
  const cookie = req.headers.cookie;
  const moderatorId = getCookie(cookie) || 'system-moderator';

  const status = action === 'approve' ? 'approved' : 'rejected';
  const result = await moderationService.moderateConversation(channelId, status, moderatorId, reason);

  // Check if moderation was successful
  if (!result.success) {
    res.status(400).json({
      success: false,
      message: result.message,
      previousStatus: result.previousStatus
    });
    return;
  }

  // Send Slack notification about the moderation result
  await moderationService.sendConversationModerationResultNotification(channelId, status, moderatorId, reason);

  // Dispatch WebSocket event to notify all connected clients
  WebSocketManager.dispatch('conversationModerationChanged', {
    channelId,
    action: status,
    moderatorId,
    reason,
    previousStatus: result.previousStatus
  });

  res.status(200).json({
    success: true,
    message: result.message,
    previousStatus: result.previousStatus
  });
});

/**
 * Moderate a message (legacy endpoint - approve or reject)
 * @route POST /moderation/moderate
 */
export const moderateMessage: RequestHandler = catchAll(async (req, res) => {
  const { messageId, action } = req.body;

  if (!messageId || !action) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Message ID and action are required');
  }

  if (!['approve', 'reject'].includes(action)) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Action must be "approve" or "reject"');
  }

  // For the new system, redirect to conversation moderation
  res.status(400).json({
    success: false,
    message: 'Message-level moderation has been replaced with conversation-level moderation. Use /moderation/moderate-conversation instead.',
    redirectTo: '/moderation/moderate-conversation'
  });
});

/**
 * Bulk moderate messages
 * @route POST /moderation/bulk-moderate
 */
export const bulkModerateMessages: RequestHandler = catchAll(async (req, res) => {
  const { messageIds, action, reason } = req.body;

  if (!messageIds || !Array.isArray(messageIds) || messageIds.length === 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Message IDs array is required');
  }

  if (!['approve', 'reject'].includes(action)) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Action must be "approve" or "reject"');
  }

  // Get moderator ID from cookie or use system default
  const cookie = req.headers.cookie;
  const moderatorId = getCookie(cookie) || 'system-moderator';

  const status = action === 'approve' ? 'approved' : 'rejected';

  // Process each message
  const results = await Promise.allSettled(
    messageIds.map(messageId =>
      moderationService.moderateMessage(messageId, status, moderatorId, reason)
    )
  );

  const successful = results.filter(r => r.status === 'fulfilled').length;
  const failed = results.filter(r => r.status === 'rejected').length;

  // Dispatch WebSocket event for bulk moderation
  if (successful > 0) {
    WebSocketManager.dispatch('moderationStatusChanged', {
      messageIds: messageIds.slice(0, successful), // Only successful ones
      action: status,
      moderatorId,
      reason,
      isBulk: true,
      successful,
      failed
    });
  }

  res.status(200).json({
    success: true,
    message: `Bulk moderation completed: ${successful} successful, ${failed} failed`,
    results: {
      successful,
      failed,
      total: messageIds.length
    }
  });
});

/**
 * Send test Slack notification
 * @route POST /moderation/test-notification
 */
export const sendTestNotification: RequestHandler = catchAll(async (_req, res) => {
  const testMessage = {
    id: 'test-id',
    messageId: 'test-message-id',
    channelId: 'test-channel',
    patientId: 'test-patient',
    messageText: 'This is a test message for moderation notification',
    patientName: 'Test Patient',
    createdAt: new Date().toISOString()
  };

  await moderationService.sendModerationNotification(testMessage);

  res.status(200).json({
    success: true,
    message: 'Test notification sent successfully'
  });
});

/**
 * Get conversation details by channel ID
 * @route GET /moderation/conversation/:channelId
 */
export const getConversationDetails: RequestHandler = catchAll(async (req, res) => {
  const { channelId } = req.params;

  if (!channelId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Channel ID is required');
  }

  const client = await db.connect();

  try {
    const query = `
      SELECT
        ccm.*,
        COALESCE(moderator_dr.username, moderator_dr.name, ccm."moderatedBy") as "moderatorName",
        tp.outcome as "treatmentOutcome",
        dr.name as "doctorName"
      FROM chat_conversation_moderation ccm
      LEFT JOIN Dr moderator_dr ON ccm."moderatedBy" = moderator_dr."accessID"
      LEFT JOIN treatmentplan tp ON ccm."treatmentPlanId" = tp.id
      LEFT JOIN Dr dr ON tp."drId" = dr."accessID"
      WHERE ccm."channelId" = $1
    `;

    const result = await client.query(query, [channelId]);

    if (result.rows.length === 0) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Conversation not found');
    }

    res.status(200).json({
      success: true,
      data: result.rows[0]
    });
  } finally {
    client.release();
  }
});

/**
 * Get messages for a conversation from Stream Chat
 * @route GET /moderation/conversation/:channelId/messages
 */
export const getConversationMessages: RequestHandler = catchAll(async (req, res) => {
  const { channelId } = req.params;
  const limit = parseInt(req.query.limit as string) || 50;

  if (!channelId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Channel ID is required');
  }

  // First verify the conversation exists in our moderation system and get stored message IDs
  const client = await db.connect();
  try {
    const checkQuery = `
      SELECT "channelId", "patientName", "moderationStatus", "messageIds"
      FROM chat_conversation_moderation
      WHERE "channelId" = $1
    `;
    const checkResult = await client.query(checkQuery, [channelId]);

    if (checkResult.rows.length === 0) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Conversation not found in moderation system');
    }

    const conversation = checkResult.rows[0];
    const storedMessageIds = conversation.messageIds;

    

    // Fetch messages from Stream Chat, filtering by stored message IDs if available
    const messagesResult = await moderationService.getChannelMessages(
      channelId,
      limit,
      storedMessageIds && storedMessageIds.length > 0 ? storedMessageIds : undefined
    );

    if (messagesResult.error) {
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, `Failed to fetch messages: ${messagesResult.error}`);
    }

    
    res.status(200).json({
      success: true,
      data: {
        conversation,
        messages: messagesResult.messages,
        messageCount: messagesResult.messages.length,
        totalStoredMessages: storedMessageIds ? storedMessageIds.length : 0,
        isFiltered: storedMessageIds && storedMessageIds.length > 0
      }
    });
  } finally {
    client.release();
  }
});

/**
 * Get message details by ID (legacy method)
 * @route GET /moderation/message/:messageId
 */
export const getMessageDetails: RequestHandler = catchAll(async (req, res) => {
  const { messageId } = req.params;

  if (!messageId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Message ID is required');
  }

  // For now, return not found since we're using conversation-based moderation
  res.status(httpStatus.NOT_FOUND).json({
    success: false,
    message: 'Message-based moderation is deprecated. Use conversation-based moderation instead.'
  });
});

/**
 * Check moderation status for conversations by channel IDs
 * @route POST /moderation/check-conversations
 */
export const checkConversationModerationStatus: RequestHandler = catchAll(async (req, res) => {
  const { channelIds } = req.body;

  if (!channelIds || !Array.isArray(channelIds) || channelIds.length === 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Channel IDs array is required');
  }

  const client = await db.connect();

  try {
    // Create placeholders for the IN clause
    const placeholders = channelIds.map((_, index) => `$${index + 1}`).join(',');

    const query = `
      SELECT "channelId", "moderationStatus", "isVisible"
      FROM chat_conversation_moderation
      WHERE "channelId" IN (${placeholders})
    `;

    const result = await client.query(query, channelIds);

    // Create a map of channelId to status
    const statusMap: Record<string, { status: string; isVisible: boolean }> = {};

    result.rows.forEach(row => {
      statusMap[row.channelId] = {
        status: row.moderationStatus,
        isVisible: row.isVisible
      };
    });

    // Fill in missing channelIds with default status
    channelIds.forEach(channelId => {
      if (!statusMap[channelId]) {
        statusMap[channelId] = {
          status: 'pending',
          isVisible: false
        };
      }
    });

    res.status(200).json({
      success: true,
      data: statusMap
    });
  } finally {
    client.release();
  }
});

/**
 * Check moderation status for multiple messages (legacy method)
 * @route POST /moderation/check-messages
 */
export const checkMessageModerationStatus: RequestHandler = catchAll(async (req, res) => {
  const { messageIds } = req.body;

  if (!messageIds || !Array.isArray(messageIds) || messageIds.length === 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Message IDs array is required');
  }

  const client = await db.connect();

  try {
    // Return empty result since we're using conversation-based moderation
    const moderationMap: Record<string, { status: string; isVisible: boolean }> = {};

    // For backward compatibility, assume all messages are approved
    messageIds.forEach(messageId => {
      moderationMap[messageId] = {
        status: 'approved',
        isVisible: true
      };
    });

    res.status(200).json({
      success: true,
      data: moderationMap,
      message: 'Message-based moderation is deprecated. Use conversation-based moderation instead.'
    });
  } finally {
    client.release();
  }
});

/**
 * Fix messageIds for a conversation (remove post-moderation messages)
 * @route POST /moderation/fix-conversation/:channelId
 */
export const fixConversationMessageIds: RequestHandler = catchAll(async (req, res) => {
  const { channelId } = req.params;

  if (!channelId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Channel ID is required');
  }

  const result = await moderationService.fixConversationMessageIds(channelId);

  if (!result.success) {
    throw new ApiError(httpStatus.BAD_REQUEST, result.message);
  }

  res.status(200).json({
    success: true,
    message: result.message
  });
});

/**
 * Get moderation history for a patient
 * @route GET /moderation/patient/:patientId/history
 */
export const getPatientModerationHistory: RequestHandler = catchAll(async (req, res) => {
  const { patientId } = req.params;
  const limit = parseInt(req.query.limit as string) || 20;

  if (!patientId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Patient ID is required');
  }

  const client = await db.connect();

  try {
    const query = `
      SELECT
        ccm.*,
        COALESCE(dr.username, dr.name, ccm."moderatedBy") as "moderatorName",
        tp.outcome as "treatmentOutcome"
      FROM chat_conversation_moderation ccm
      LEFT JOIN Dr dr ON ccm."moderatedBy" = dr."accessID"
      LEFT JOIN treatmentplan tp ON ccm."treatmentPlanId" = tp.id
      WHERE ccm."patientId" = $1
      ORDER BY ccm."createdAt" DESC
      LIMIT $2
    `;

    const result = await client.query(query, [patientId, limit]);

    res.status(200).json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });
  } finally {
    client.release();
  }
});