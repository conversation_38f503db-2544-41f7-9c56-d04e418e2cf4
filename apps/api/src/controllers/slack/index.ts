import { WebClient } from '@slack/web-api';
import { RequestHandler } from 'express-serve-static-core';
import config from '../../config';
import { catchAll } from '../../utils/catchAll';
import { AxiosError } from 'axios';
import { ApiError } from '../../utils/ApiError';
import httpStatus from 'http-status';
import { db } from '../../utils/db';
import { DateTime } from 'luxon';
import {
  verifyNoShowPatient,
  verifyPreviousConsultation,
  verifyPreviousIfPatientHasAnyPreviousBooking,
  verifyPreviousNoShowConsultation,
} from '../common';
import { DoctorQueueResult, PatientConsultationData, PatientQueueDetailsData, QueueResult } from '../../types';
import { calculateConsultationTime } from '../../utils/report';
import { logger } from '../../config/logger';

const slack = new WebClient(config.slackToken); // Use environment variable

export const sendReportToSlack: RequestHandler = catchAll(async (req, res) => {
  // send request to slack
  const client = await db.connect();

  const requester = req.body.user_name ?? undefined;
  const channelName = req.body.channel_name ?? undefined;
  const channelID = req.body.channel_id ?? config.slackReportChannel;
  const doctorId = req.body.drId ?? undefined;
  // const filterDate = req.body.text ?? today

  const filterDate = req.body.text ? DateTime.fromFormat(req.body.text, 'yyyy-MM-dd') : DateTime.utc();
  const startDate = filterDate.toFormat('yyyy-MM-dd');
  const endDate = filterDate.plus({ days: 1 }).toFormat('yyyy-MM-dd');

  try {
    // Base doctor filter condition
    const doctorFilter = doctorId ? `AND "drId" = '${doctorId}'` : '';

    const combinedQuery = `
        SELECT
            COUNT(*) AS total_tps,
            COUNT(CASE WHEN outcome = 'Reject' AND tp.source = 'consultation' THEN 1 END) AS total_reject,
            COUNT(CASE WHEN outcome = 'Approve Unrestricted' AND tp.source = 'consultation' THEN 1 END) AS total_approved_unrestricted,
            COUNT(CASE WHEN outcome = 'Approve Subject To GP Referral' AND tp.source = 'consultation' THEN 1 END) AS total_gp_referrals,
            COUNT(CASE WHEN outcome = 'Approve Subject To Discharge Form' AND tp.source = 'consultation' THEN 1 END) AS total_discharge_letter,
            COUNT(CASE WHEN tp.source = 'consultation' THEN 1 END) AS consultation_tps,
            COUNT(CASE WHEN tp.source = 'messenger' THEN 1 END) AS messenger_tps
        FROM treatmentplan tp
        LEFT JOIN Patient p ON tp."patientID" = p."patientID"
        WHERE tp."createdAt" >= '${startDate}'
        AND tp."createdAt" < '${endDate}'
        AND (p.email IS NULL OR p.email != '${config.zelda.patientEmail}')
        AND (tp.type IS NULL OR tp.type != 'zelda-automated')
        ${doctorFilter.replace('"drId"', 'tp."drId"')};
    `;

    const neverAdmittedQuery = `
    SELECT COUNT(*) AS total_count
        FROM "patientqueue"
        WHERE "joinedAt" IS NOT NULL
        AND "completedAt" IS NULL
        AND "admittedAt" IS NULL
        ${doctorId ? `AND "assignedDoctorID" = '${doctorId}'` : ''}
        AND "updatedAt" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND "updatedAt" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
    `;
    const neverJoinedCall = `
    SELECT COUNT(*) AS total_count
        FROM "patientqueue"
        WHERE "joinedAt" IS NOT NULL
        AND "completedAt" IS NULL
        ${doctorId ? `AND "assignedDoctorID" = '${doctorId}'` : ''}
        AND "updatedAt" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND "updatedAt" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
        AND status = 'ADMITTED'
        `;

    const patientWhoJoinedAndDroppedOff = `
        SELECT COUNT(*) AS total_count
          FROM "patientqueue"
          WHERE "joinedAt" IS NOT NULL
          AND "completedAt" IS NULL
          AND "admittedAt" IS NOT NULL
          ${doctorId ? `AND "assignedDoctorID" = '${doctorId}'` : ''}
          AND "updatedAt" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
          AND "updatedAt" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
          AND "noShow" = TRUE
        `;

    const patientWhoWereInvitedButNeverCameOnline = `
    SELECT COUNT(*) AS total_count
        FROM "patientqueue"
        WHERE "joinedAt" IS NULL
        ${doctorId ? `AND "assignedDoctorID" = '${doctorId}'` : ''}
        AND "updatedAt" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND "updatedAt" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
        AND status = 'OFFLINE'
      `;
    // Update this with the correct tech issue column
    const techIssue = `
    SELECT COUNT(*) AS total_count
        FROM "patientqueue"
        WHERE "joinedAt" IS NOT NULL
        AND "completedAt" IS NULL
        ${doctorId ? `AND "assignedDoctorID" = '${doctorId}'` : ''}
        AND "updatedAt" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND "updatedAt" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
        AND tech_issue = TRUE
    `;

    const durationQuery = `
        WITH first_start AS (
            SELECT MIN("createdAt") AS first_start_time
            FROM doctorStartEndTimer
            WHERE action = 'start'
            AND "createdAt" >= '${startDate}'
            AND "createdAt" < '${endDate}'
            ${doctorId ? `AND "drId" = '${doctorId}'` : ''}
        ),
        last_end AS (
            SELECT MAX("createdAt") AS last_end_time
            FROM doctorStartEndTimer
            WHERE (action = 'end' OR action = 'delete')
            AND "createdAt" >= '${startDate}'
            AND "createdAt" < '${endDate}'
            ${doctorId ? `AND "drId" = '${doctorId}'` : ''}
        ),
        sydney_times AS (
            SELECT
                fs.first_start_time,
                le.last_end_time,
                fs.first_start_time AT TIME ZONE 'Australia/Sydney' AS first_start_time_sydney,
                le.last_end_time AT TIME ZONE 'Australia/Sydney' AS last_end_time_sydney,
                NOW() AT TIME ZONE 'Australia/Sydney' AS now_sydney
            FROM first_start fs
            LEFT JOIN last_end le ON true
        )
        SELECT
            first_start_time,
            last_end_time,
            COALESCE(
                EXTRACT(EPOCH FROM (last_end_time_sydney - first_start_time_sydney))/3600,
                EXTRACT(EPOCH FROM (now_sydney - first_start_time_sydney))/3600
            ) AS duration_hours
        FROM sydney_times;
    `;

    // Query to get detailed doctor session information during timer period
    const doctorSessionQuery = `
        WITH timer_period AS (
            -- Get the consultation timer start and end times for the day
            SELECT
                MIN(CASE WHEN action = 'start' THEN "createdAt" END) AS timer_start,
                MAX(CASE WHEN action = 'end' OR action = 'delete' THEN "createdAt" END) AS timer_end
            FROM doctorStartEndTimer
            WHERE "createdAt" >= '${startDate}'
            AND "createdAt" < '${endDate}'
            ${doctorId ? `AND "drId" = '${doctorId}'` : ''}
        ),
        timer_sessions AS (
            SELECT
                ds."doctorId",
                ds.action,
                ds.timestamp AT TIME ZONE 'Australia/Sydney' AS sydney_time,
                ds."sessionDuration",
                tp.timer_start,
                tp.timer_end,
                ROW_NUMBER() OVER (PARTITION BY ds."doctorId" ORDER BY ds.timestamp) AS session_order
            FROM doctor_sessions ds
            CROSS JOIN timer_period tp
            INNER JOIN dr d ON ds."doctorId" = d."accessID"
            WHERE ds.timestamp >= tp.timer_start - INTERVAL '2 hours'
            AND ds.timestamp <= COALESCE(tp.timer_end, NOW()) + INTERVAL '2 hours'
            AND d.role = 'doctor'
            ${doctorId ? `AND ds."doctorId" = '${doctorId}'` : ''}
            AND tp.timer_start IS NOT NULL
        ),
        paired_sessions AS (
            -- Pair each logout with its corresponding login
            SELECT
                "doctorId",
                action,
                sydney_time,
                "sessionDuration",
                LAG(sydney_time) OVER (PARTITION BY "doctorId" ORDER BY sydney_time) AS prev_time,
                LAG(action) OVER (PARTITION BY "doctorId" ORDER BY sydney_time) AS prev_action
            FROM timer_sessions
        ),
        calculated_sessions AS (
            -- Use the stored sessionDuration from logout events (which should be correct)
            SELECT
                "doctorId",
                sydney_time AS logout_time,
                "sessionDuration" AS session_seconds
            FROM timer_sessions
            WHERE action = 'LOGOUT'
            AND "sessionDuration" IS NOT NULL
            AND "sessionDuration" > 0
        ),
        session_summary AS (
            SELECT
                "doctorId",
                MIN(CASE WHEN action = 'LOGIN' THEN sydney_time END) AS first_login,
                MAX(CASE WHEN action = 'LOGOUT' THEN sydney_time END) AS last_logout,
                STRING_AGG(
                    DISTINCT CASE WHEN action = 'LOGIN' THEN TO_CHAR(sydney_time, 'HH12:MI AM') END,
                    ', ' ORDER BY CASE WHEN action = 'LOGIN' THEN TO_CHAR(sydney_time, 'HH12:MI AM') END
                ) FILTER (WHERE action = 'LOGIN') AS all_logins,
                STRING_AGG(
                    DISTINCT CASE WHEN action = 'LOGOUT' THEN TO_CHAR(sydney_time, 'HH12:MI AM') END,
                    ', ' ORDER BY CASE WHEN action = 'LOGOUT' THEN TO_CHAR(sydney_time, 'HH12:MI AM') END
                ) FILTER (WHERE action = 'LOGOUT') AS all_logouts
            FROM timer_sessions
            GROUP BY "doctorId"
        ),
        total_work_calculation AS (
            SELECT
                "doctorId",
                SUM(session_seconds) AS total_session_seconds
            FROM calculated_sessions
            GROUP BY "doctorId"
        )
        SELECT
            ss.first_login,
            ss.last_logout,
            COALESCE(twc.total_session_seconds / 3600.0, 0) AS total_work_hours,
            COALESCE(
                EXTRACT(EPOCH FROM (ss.last_logout - ss.first_login)) / 3600.0,
                0
            ) AS work_span_hours,
            ss.all_logins,
            ss.all_logouts
        FROM session_summary ss
        LEFT JOIN total_work_calculation twc ON ss."doctorId" = twc."doctorId";
    `;

    // If doctorId is provided, get doctor name
    let doctorName = '';
    if (doctorId) {
      const doctorQuery = `
        SELECT username FROM Dr WHERE "accessID" = '${doctorId}' LIMIT 1;
      `;
      const doctorResult = await client.query(doctorQuery);
      if (doctorResult.rows.length > 0) {
        doctorName = doctorResult.rows[0].username;
      }
    }

    const tpReport = await client.query(combinedQuery);
    const durationReport = await client.query(durationQuery);
    const sessionReport = await client.query(doctorSessionQuery);

    const neverAdmittedQueryRes = await client.query(neverAdmittedQuery);
    const neverJoinedCallRes = await client.query(neverJoinedCall);
    const patientWhoJoinedAndDroppedOffRes = await client.query(patientWhoJoinedAndDroppedOff);
    const patientWhoWereInvitedButNeverCameOnlineRes = await client.query(patientWhoWereInvitedButNeverCameOnline);

    // Uncomment this when you update techIssue query
    const techIssueRes = await client.query(techIssue);

    // build message
    const stats = tpReport.rows[0]; // Assuming your query result is in `rows[0]`
    const duration = durationReport.rows[0]; // Get duration
    const sessionData = sessionReport.rows[0]; // Get doctor session data

    // Format duration to hours and minutes
    let formattedDuration = 'No data available';
    let formattedStartTime = 'No data available';
    let formattedEndTime = 'No data available';
    let formattedActiveSession = 'No data available';

    if (duration && duration.duration_hours !== null) {
      const durationHours = Math.floor(duration.duration_hours);
      const durationMinutes = Math.round((duration.duration_hours - durationHours) * 60);
      formattedDuration = `${durationHours}h ${durationMinutes}m`;

      // Format start time
      if (duration.first_start_time) {
        const startTime = DateTime.fromJSDate(new Date(duration.first_start_time)).setZone('Australia/Sydney');
        formattedStartTime = startTime.toFormat('h:mm a'); // 12-hour format with AM/PM
      }

      // Format end time
      if (duration.last_end_time) {
        const endTime = DateTime.fromJSDate(new Date(duration.last_end_time)).setZone('Australia/Sydney');
        formattedEndTime = endTime.toFormat('h:mm a'); // 12-hour format with AM/PM
      } else if (duration.first_start_time) {
        formattedEndTime = 'Still in progress';
      }
    }

    // Format session data
    //let formattedWorkSpan = 'No data available';
    let formattedFirstLogin = 'No data available';
    let formattedLastLogout = 'No data available';
    let allLoginsText = 'No data available';
    let allLogoutsText = 'No data available';

    if (sessionData && sessionData.total_work_hours !== null) {
      // Format total work hours (actual logged in time)
      const workHours = Math.floor(sessionData.total_work_hours);
      const workMinutes = Math.round((sessionData.total_work_hours - workHours) * 60);
      formattedActiveSession = `${workHours}h ${workMinutes}m`;

      // Format work span (first login to last logout)
      // if (sessionData.work_span_hours > 0) {
      //   const spanHours = Math.floor(sessionData.work_span_hours);
      //   const spanMinutes = Math.round((sessionData.work_span_hours - spanHours) * 60);
      //   formattedWorkSpan = `${spanHours}h ${spanMinutes}m`;
      // }

      // Format first login and last logout times (already in Sydney timezone from query)
      if (sessionData.first_login) {
        const firstLogin = DateTime.fromJSDate(new Date(sessionData.first_login));
        formattedFirstLogin = firstLogin.toFormat('h:mm a');
      }

      if (sessionData.last_logout) {
        const lastLogout = DateTime.fromJSDate(new Date(sessionData.last_logout));
        formattedLastLogout = lastLogout.toFormat('h:mm a');
      }

      // Format all logins and logouts
      allLoginsText = sessionData.all_logins || 'None';
      allLogoutsText = sessionData.all_logouts || 'None';
    }

    // Add tech issues before no shows
    const message = `
    *📊 Consultation Report for ${startDate}${doctorId ? ' (Doctor Specific)' : ''}*
    \`\`\`
    ==============================================
    CATEGORY                              COUNT
    ==============================================
    Total Completed Consultation          ${stats.consultation_tps}
    Approved (Unrestricted)               ${stats.total_approved_unrestricted}
    Rejected                              ${stats.total_reject}
    Discharge Letters                     ${stats.total_discharge_letter}
    Pending GP Referrals                  ${stats.total_gp_referrals}

    OTHER TREATMENT PLAN SOURCES:
    Messenger-based                       ${stats.messenger_tps}

    Never Admitted                        ${neverAdmittedQueryRes.rows[0]?.total_count}
    Drop Off (Didn’t join consultation)   ${neverJoinedCallRes.rows[0]?.total_count}
    Drop Off (Mid consultation)           ${patientWhoJoinedAndDroppedOffRes.rows[0]?.total_count}
    No Shows                              ${patientWhoWereInvitedButNeverCameOnlineRes.rows[0]?.total_count}
    Tech Issues                           ${techIssueRes.rows[0]?.total_count}
    ================================

    Start Time:                 ${formattedStartTime}
    End Time:                   ${formattedEndTime}
    Consultation Duration:      ${formattedDuration}

    DOCTOR SESSION SUMMARY:
    First Login:                ${formattedFirstLogin}
    Last Logout:                ${formattedLastLogout}
    Total Work Time:            ${formattedActiveSession}

    SESSION DETAILS:
    All Logins:                 ${allLoginsText}
    All Logouts:                ${allLogoutsText}${
      doctorName
        ? `

    Doctor:                     ${doctorName}`
        : ''
    }
    ================================

    Env: ${config.devMode ? 'Local or Staging' : 'Production'}
    Requester: ${requester ?? 'Admin'}
    Requested from: ${channelName ? channelName + ' Slack Channel' : 'Dr UI'}
    \`\`\`
    `;
    // Send message to channel
    await slack.chat.postMessage({
      channel: channelID, // Replace with channel ID
      text: message,
    });
    res.status(200).send({});
    return;
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const sendDetailedReportToSlack: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();

  const query = `
  WITH LatestConsultation AS (
        SELECT DISTINCT ON (c."patientID") c.*
        FROM Consultation c
        ORDER BY c."patientID", c."createdAt" DESC, c."consultationDate" DESC
      ),
      LatestPerPatient AS (
        SELECT DISTINCT ON (c2."patientID") c2.*
        FROM Consultation c2
        ORDER BY c2."patientID", c2."createdAt" DESC, c2."consultationDate" DESC
      )
      SELECT  
        dr.username AS "drUsername", 
        pq."assignedDoctorID", 
        p."zohoID", 
        lc.*
      FROM LatestConsultation lc
      JOIN patient p ON p.email = lc.email
      LEFT JOIN patientqueue pq ON pq.email = lc.email
      LEFT JOIN dr ON dr."accessID" = pq."assignedDoctorID"
      WHERE
        lc."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND lc."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
        AND NOT EXISTS (
          SELECT 1
          FROM LatestPerPatient lpp
          WHERE lpp."patientID" = lc."patientID"
            AND lpp."notificationSent" = false
            AND lpp."completed" = true
        );
  `;

  const checkDocSession = `
  SELECT ls.*
    FROM doctor_sessions ls
    LEFT JOIN doctor_sessions lo
      ON ls."sessionId" = lo."sessionId"
      AND lo."action" = 'LOGOUT'
    WHERE ls."action" = 'LOGIN'
      AND ls."timestamp" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
      AND ls."timestamp" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
      AND lo."sessionId" IS NULL;
      `;
  try {
    const today = DateTime.utc().toFormat('yyyy-MM-dd');
    logger.info(`Sending Report (${today}) ...`);
    const docOpenSession = await client.query(checkDocSession);

    if (docOpenSession.rows.length > 0) {
      logger.info(`Report will not send yet because we still have ${docOpenSession.rows.length} open sessions`);
      logger.info(`Sending Report (${today}) ... DONE`);
      res.status(200).send({});
      return;
    }

    const result = await client.query(query);
    const consultationToday = result.rows as PatientConsultationData[];

    const newPatient: PatientConsultationData[] = [];
    const previousNoShowWhoShowedUp: PatientConsultationData[] = []; // previous no shows, that where no shows today.
    const firstTimeComer: PatientConsultationData[] = [];
    const patientWhoBookedWithUsBefore: PatientConsultationData[] = [];
    const patientWhoNoShowWithAPreviousNoShow: PatientConsultationData[] = [];
    const patientWhoWithAnyPreviousBookingWhoHadConsultationToday: PatientConsultationData[] = [];

    for (const consultation of consultationToday) {
      if (consultation.email) {
        const previousNoShowPatient = await verifyNoShowPatient(consultation.email, 0);
        const patientWithAnyPreviousBooking = await verifyPreviousIfPatientHasAnyPreviousBooking(consultation.email);
        const noShowRecord = await verifyPreviousNoShowConsultation(consultation.email);
        const newPatientCheck = await verifyPreviousConsultation(consultation.email);
        if (noShowRecord.length < 1) {
          newPatient.push(consultation);
        } else {
          previousNoShowWhoShowedUp.push(consultation);
        }

        if (newPatientCheck.length <= 0) {
          firstTimeComer.push(consultation);
        } else {
          patientWhoBookedWithUsBefore.push(consultation);
        }

        if (previousNoShowPatient.length > 1) {
          patientWhoNoShowWithAPreviousNoShow.push({
            ...consultation,
            noShowNumbers: previousNoShowPatient.length,
          });
        }

        if (patientWithAnyPreviousBooking) {
          patientWhoWithAnyPreviousBookingWhoHadConsultationToday.push(consultation);
        }
      }
    }

    const newComerWhoWereInvited = firstTimeComer.filter((f) => f.notificationSent);
    const newComerWhoWereNotInvited = firstTimeComer.filter((f) => !f.notificationSent);
    const newComerWhoWereNoShow = firstTimeComer.filter((f) => f.queueTag == 'no-show');
    const newComerWhoShowedUp = firstTimeComer.filter((f) => f.queueTag == 'showed-up');

    const previousBookingWhoNoShowed = patientWhoWithAnyPreviousBookingWhoHadConsultationToday.filter(
      (f) => f.queueTag === 'no-show',
    );
    const previousBookingWhoShowedUp = patientWhoWithAnyPreviousBookingWhoHadConsultationToday.filter(
      (f) => f.queueTag === 'showed-up',
    );

    const messageMainChannel = `
        
      *CONSULTATION REPORT FOR* ${today}\n
      The report include all consultation today. 
      ==========================\n\n
      *Total consultations today at the moment this report is triggered* (${consultationToday.length})\n\n
      1. New Leads who were invited today: ${newComerWhoWereInvited.length} (${((newComerWhoWereInvited.length / consultationToday.length) * 100).toFixed(2)} %)\n
      2. New Leads who were not invited today: ${newComerWhoWereNotInvited.length} (${((newComerWhoWereNotInvited.length / consultationToday.length) * 100).toFixed(2)} %)\n
      3. New Leads who showed up today: ${newComerWhoShowedUp.length} (${((newComerWhoShowedUp.length / consultationToday.length) * 100).toFixed(2)} %)\n
      4. New Leads who were no show today: ${newComerWhoWereNoShow.length} (${((newComerWhoWereNoShow.length / consultationToday.length) * 100).toFixed(2)} %)\n
      5. Patients who were no show in their previous consultation and showed up today: ${previousNoShowWhoShowedUp.length} (${((previousNoShowWhoShowedUp.length / consultationToday.length) * 100).toFixed(2)} %)\n
      6. Patients who were no show in their previous consultation and no-showed today: ${patientWhoNoShowWithAPreviousNoShow.length} (${((patientWhoNoShowWithAPreviousNoShow.length / consultationToday.length) * 100).toFixed(2)} %)\n
      7. Patients with any previous booking who showed up today: ${previousBookingWhoShowedUp.length} (${((previousBookingWhoShowedUp.length / consultationToday.length) * 100).toFixed(2)} %)\n
      8. Patients with any previous booking who no-showed today: ${previousBookingWhoNoShowed.length} (${((previousBookingWhoNoShowed.length / consultationToday.length) * 100).toFixed(2)} %)
      `;

    const messageSubChannel = `
        CONSULTATION REPORT FOR ${today}\n\n
      *Total consultations today at the moment this report is triggered* (${consultationToday.length})\n
  
      ======================================================= \n
      New Leads who were invited today: ${newComerWhoWereInvited.length}\n
      ${newComerWhoWereInvited.map((p, i) => `${i + 1}. ${p.email}`).join('\n')}
      ======================================================= \n

      New Leads who were not invited today: ${newComerWhoWereNotInvited.length}\n
      ${newComerWhoWereNotInvited.map((p, i) => `${i + 1}. ${p.email}`).join('\n')}
      ======================================================= \n

      New Leads who showed up today: ${newComerWhoShowedUp.length}\n
      ${newComerWhoShowedUp.map((p, i) => `${i + 1}. ${p.email}`).join('\n')}
      ======================================================= \n

      New Leads who were no show today: ${newComerWhoWereNoShow.length}\n
      ${newComerWhoWereNoShow.map((p, i) => `${i + 1}. ${p.email}`).join('\n')}
      ======================================================= \n

      Patients who were no show in their previous consultation and showed up today: ${previousNoShowWhoShowedUp.length}\n
      ${previousNoShowWhoShowedUp.map((p, i) => `${i + 1}. ${p.email}`).join('\n')}
      ======================================================= \n

      Patients who were no show in their previous consultation and no-showed today: ${patientWhoNoShowWithAPreviousNoShow.length}\n
      ${patientWhoNoShowWithAPreviousNoShow.map((p, i) => `${i + 1}. ${p.email}. Number of consecutive no shows: ${p.noShowNumbers}`).join('\n')}
      ======================================================= \n

      Patients with any previous booking who showed up today: ${previousBookingWhoShowedUp.length}\n
      ${previousBookingWhoShowedUp.map((p, i) => `${i + 1}. ${p.email}`).join('\n')}
      ======================================================= \n

      Patients with any previous booking who no-showed today: ${previousBookingWhoNoShowed.length}
      ${previousBookingWhoNoShowed.map((p, i) => `${i + 1}. ${p.email}`).join('\n')}
      `;

    // https://harvest-australia.slack.com/archives/C08FW6GA9U4 Test
    // https://harvest-australia.slack.com/archives/C08P75RU7RQ MainChannel
    // https://harvest-australia.slack.com/archives/C092N64A93Q SubChannel

    await slack.chat.postMessage({
      channel: 'C08P75RU7RQ', // Replace with channel ID
      text: messageMainChannel,
    });

    await slack.chat.postMessage({
      channel: 'C092N64A93Q', // Replace with channel ID
      text: messageSubChannel,
    });

    await detailedReportOnPatientQueue(consultationToday);

    logger.info(`Report successfully sent to slack (${today})`);
    logger.info(`Sending Report (${today}) ... DONE`);

    res.status(200).send({
      previousNoShowWhoShowedUp,
      newComerWhoWereInvited,
      newComerWhoWereNoShow,
      patientWhoBookedWithUsBefore,
      patientWhoNoShowWithAPreviousNoShow,
      patientWhoWithAnyPreviousBookingWhoHadConsultationToday,
    });
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const detailedReportOnPatientQueue = async (data: PatientConsultationData[]) => {
  const client = await db.connect();
  const pqTable = `SELECT * FROM patientqueuedetails pq WHERE pq.email = $1 ORDER BY "createdAt" ASC`;
  try {
    const consultationToday = data;
    const allResult: QueueResult = {};
    const allResult2 = {};

    for (const consultation of consultationToday) {
      const res = await client.query(pqTable, [consultation.email]);
      const patientData = res.rows as PatientQueueDetailsData[];
      const consultationTime = calculateConsultationTime(patientData);
      allResult[consultation.email] = {
        doctor: consultation.drUsername,
        email: consultation.email,
        consultationTime,
        zohoID: consultation.zohoID,
      };

      allResult2[consultation.email] = patientData.map((d) => {
        const createdAt = DateTime.fromJSDate(new Date(d.createdAt), { zone: 'Australia/Sydney' });
        return {
          ...d,
          createdAt,
        };
      });
    }

    const doctors: DoctorQueueResult = {};

    Object.keys(allResult).forEach((email) => {
      const patient = allResult[email];
      const doctor = patient.doctor || 'Unassigned';

      if (!doctors[doctor]) {
        doctors[doctor] = [];
      }

      doctors[doctor].push({
        ...patient,
        email,
      });
    });

    const message = Object.entries(doctors)
      .map(([doctorName, patients]) => {
        if (doctorName !== 'Unassigned') {
          const header = `*👨‍⚕️ Doctor: ${doctorName}*`;
          const patientLines = patients.map((p, i) => {
            return `${i + 1}. ${p.email} | ⏱️ ${p.consultationTime ?? 'N/A'} | 🆔 ${p.consultationTime ? `<https://crm.zoho.com.au/crm/org7002688441/tab/Contacts/${p.zohoID}|${p.zohoID}>` : `${p.zohoID}`}`;
          });
          return `${header}\n${patientLines.join('\n')}`;
        }
        return '';
      })
      .join('\n\n');

    const today = DateTime.utc().toFormat('yyyy-MM-dd');

    const headers = `*CONSULTATION TIME REPORT FOR EACH DOCTOR* (${today})`;
    await slack.chat.postMessage({
      channel: 'C08P75RU7RQ',
      text: `${headers}\n\n ${message}`,
    });

    return;
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
};