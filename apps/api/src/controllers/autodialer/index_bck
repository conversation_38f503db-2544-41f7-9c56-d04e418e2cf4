import twilio from 'twilio';
import { <PERSON><PERSON><PERSON>and<PERSON> } from 'express';
import { catchAll } from '../../utils/catchAll';
import { ZohoAuth, zohoLeadURL } from '../../helpers/zoho';
import axios, { AxiosError } from 'axios';
import { db } from '../../utils/db';
import {logger} from '../../config/logger'
// ==================== CONFIGURATION ====================
interface AppConfig {
  SERVER_URL: string;
  ACCOUNT_SID: string;
  AUTH_TOKEN: string;
  TWILIO_NUMBERS: [string, string];
  AGENT_SIP: string;
  CALL_TIMEOUT: number;
  MACHINE_DETECTION_SENSITIVITY: number;
  VOICEMAIL_MESSAGE: string;
}

interface LeadData {
  leadId: string;
  name: string;
  mobile: string | null;
  phone: string | null;
}

const config: AppConfig = {
  CALL_TIMEOUT: 45,
  MACHINE_DETECTION_SENSITIVITY: 5000,
  VOICEMAIL_MESSAGE: 'https://chestnut-herring-1214.twil.io/assets/voicemail.mp3',
  SERVER_URL: 'https://10a2-197-184-126-85.ngrok-free.app/api/zoho',
  //SERVER_URL: 'https://zenith.ad/api/zoho',
  ACCOUNT_SID: '**********************************',
  AUTH_TOKEN: '5846f60055bbc9436837f5b6d9f866ea',
  //TWILIO_NUMBERS: ['+***********', '+***********'],

 TWILIO_NUMBERS: ['+***********', '+***********'],
  AGENT_SIP: 'sip:+<EMAIL>',
};

const client = twilio(config.ACCOUNT_SID, config.AUTH_TOKEN);
const leadsDictionary: Record<string, LeadData> = {};

// ==================== STATE MANAGEMENT ====================
type CallStatus = 'dialing' | 'dialing-agent' | 'answered' | 'voicemail' | 'completed';
type AnsweredBy = 'human' | 'machine' | 'no-answer';
type AgentStatus = 'available' | 'busy' | 'wrapping_up';

const dialerState = {
  customerQueue: [] as string[],
  activeCalls: new Map<string, {
    twilioNumber: string;
    customerNumber: string;
    status: CallStatus;
    answeredBy?: AnsweredBy;
    leadId?: string;
  }>(),

  callResults: {
    answered: 0,
    voicemail: 0,
    failed: 0
  },

  get totalCalls() {
    return this.callResults.answered +
      this.callResults.voicemail +
      this.callResults.failed;
  },

  availableNumbers: [...config.TWILIO_NUMBERS],
  agentStatus: 'available' as AgentStatus,
  currentAgentCallSid: null as string | null,

  canTakeCall: () => {
    return dialerState.agentStatus === 'available' &&
      dialerState.currentAgentCallSid === null;
  },

  getAvailableNumber: () => {
    return dialerState.availableNumbers[0];
  }
};

// Helper function to safely return numbers to pool
function returnNumberToPool(number: string) {
  if (number && !dialerState.availableNumbers.includes(number)) {
    dialerState.availableNumbers.push(number);
  }
}

// ==================== CONTROL ENDPOINTS ====================
export const startAutodialer: RequestHandler = catchAll(async (_req, res) => {
  await refreshLeadsDictionary();

  dialerState.availableNumbers = [...config.TWILIO_NUMBERS];
  dialerState.activeCalls.clear();
  dialerState.agentStatus = 'available';
  dialerState.currentAgentCallSid = null;

  res.json({
    success: true,
    message: 'Dialer initialized - ready to receive calls',
    queueLength: Object.keys(leadsDictionary).length
  });

  processQueue();
});

export const getStatus: RequestHandler = catchAll(async (_req, res) => {
  const activeCalls = Array.from(dialerState.activeCalls.values()).map(call => ({
    ...call,
    leadName: call.leadId ? leadsDictionary[call.leadId]?.name : 'unknown'
  }));

  res.json({
    activeCalls,
    availableNumbers: dialerState.availableNumbers,
    agentStatus: dialerState.agentStatus,
    currentAgentCallSid: dialerState.currentAgentCallSid,
    callStats: dialerState.callResults
  });
});

// Validate Australian mobile number patterns
const validateAndFormat = (number?: string): string | null => {
  if (!number) return null;

  const cleaned = number.replace(/\D/g, '');

  if (cleaned.startsWith('61') && cleaned.length === 11) {
    return `+${cleaned}`;
  }
  if (cleaned.startsWith('+61') && cleaned.length === 12) {
    return number;
  }

  const isValid = (
    (/^04\d{8}$/.test(cleaned) && cleaned.length === 10) ||
    (/^4\d{8}$/.test(cleaned) && cleaned.length === 9) ||
    (/^614\d{8}$/.test(cleaned) && cleaned.length === 11)
  );

  if (!isValid) {
    throw new Error(`Invalid Australian number format: ${number}`);
  }

  if (cleaned.startsWith('04')) return `+61${cleaned.substring(1)}`;
  if (cleaned.startsWith('4')) return `+61${cleaned}`;
  if (cleaned.startsWith('614')) return `+${cleaned}`;

  return null;
};

// ==================== LEAD MANAGEMENT ====================
export const createLeads: RequestHandler = catchAll(async (req, res) => {
  const { leadId, mobile, phone, fullName, agent } = req.body;

  const client = await db.connect();

  try {
    const existingLead = await client.query(
      `SELECT lead_id, mobile, phone, full_name, agent 
       FROM leads WHERE lead_id = $1 LIMIT 1`,
      [leadId]
    );

    const formattedMobile = validateAndFormat(mobile);
    const formattedPhone = validateAndFormat(phone);

    if (!formattedMobile && !formattedPhone) {
      throw new Error('At least one valid contact number required');
    }

    if (existingLead.rows.length > 0) {
      const current = existingLead.rows[0];
      const changes = {
        mobile: formattedMobile !== current.mobile,
        phone: formattedPhone !== current.phone,
        fullName: fullName !== current.full_name,
        agent: agent !== current.agent
      };

      if (Object.values(changes).some(Boolean)) {
        await client.query(
          `UPDATE leads SET
            mobile = COALESCE($2, mobile),
            phone = COALESCE($3, phone),
            full_name = COALESCE($4, full_name),
            agent = COALESCE($5, agent),
            status = 'new',
            status_id = (SELECT status_id FROM lead_status WHERE status_name = 'new'),
            updated_at = NOW()
           WHERE lead_id = $1
           RETURNING *`,
          [leadId, formattedMobile, formattedPhone, fullName, agent]
        );

        await refreshLeadsDictionary();
        client.release();
        res.status(200).json({ message: 'Lead updated successfully' });
        return;
      }

      client.release();
      res.status(200).json({ message: 'Lead exists with identical data' });
      return;
    }

    const result = await client.query(
      `INSERT INTO leads (lead_id, mobile, phone, full_name, agent, status_id)
       VALUES ($1, $2, $3, $4, $5, 
         (SELECT status_id FROM lead_status WHERE status_name = 'new'))
       RETURNING *`,
      [leadId, formattedMobile, formattedPhone, fullName, agent]
    );

    await refreshLeadsDictionary();
    await initialCall();

    client.release();
    res.status(201).json(result.rows[0]);

  } catch (e) {
    client.release();
    const err = e as AxiosError;

    if (err.code === '23505') {
      res.status(200).json({ message: 'Lead already exists' });
      return;
    }

    if (err.message.includes('Invalid Australian number format') ||
      err.message.includes('valid contact number')) {

      createInvalidLead(req.body);
      await updateZohoLeadStatus(leadId, 'invalid');

      res.status(400).json({ error: err.message });
      return;
    }

    console.error('Lead creation failed:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

const createInvalidLead = async (leadData) => {
  const { leadId, mobile, phone, fullName, agent } = leadData;
  const client = await db.connect();

  try {
    const result = await client.query(
      `INSERT INTO leads (lead_id, mobile, phone, full_name, agent, status, status_id)
       VALUES ($1, $2, $3, $4, $5,'invalid',
         (SELECT status_id FROM lead_status WHERE status_name = 'invalid'))
       RETURNING *`,
      [leadId, mobile, phone, fullName, agent]
    );

    await client.query('COMMIT');
    return result.rows[0];
  } catch (e) {
    const err = e as AxiosError;
    await client.query('ROLLBACK');

    if (err.code === '23505') {
      throw new Error('Lead already exists');
    }

    console.error('Failed to create invalid lead:', err);
    throw err;
  } finally {
    client.release();
  }
};

// ==================== HELPER FUNCTIONS ====================
export async function initialCall(): Promise<void> {
  try {
    dialerState.availableNumbers = [...config.TWILIO_NUMBERS];
    dialerState.activeCalls.clear();
    dialerState.agentStatus = 'available';
    dialerState.currentAgentCallSid = null;

    logger.info('Autodialer initialized - ready to receive calls');
    await processQueue();

  } catch (error) {
    console.error('Failed to initialize autodialer:', error);
    dialerState.availableNumbers = [...config.TWILIO_NUMBERS];
    throw error;
  }
}

export async function refreshLeadsDictionary() {
  const client = await db.connect();

  try {
    const leadsRes = await client.query<{
      lead_id: string;
      full_name: string;
      mobile: string | null;
      phone: string | null;
    }>(
      `SELECT l.lead_id, l.full_name, l.mobile, l.phone
       FROM leads l
       WHERE l.status_id = (
         SELECT status_id 
         FROM lead_status 
         WHERE status_name = 'new'
         LIMIT 1
       )
       AND NOT EXISTS (
         SELECT 1 
         FROM leads l2
         JOIN lead_status ls ON l2.status_id = ls.status_id
         WHERE l2.lead_id = l.lead_id
         AND ls.status_name IN ('success')
       )`
    );

    Object.keys(leadsDictionary).forEach(key => delete leadsDictionary[key]);

    leadsRes.rows.forEach(lead => {
      leadsDictionary[lead.lead_id] = {
        leadId: lead.lead_id,
        name: lead.full_name,
        mobile: lead.mobile,
        phone: lead.phone
      };
    });

    logger.info(`Refreshed leads dictionary with ${leadsRes.rowCount} NEW leads`);

  } catch (error) {
    console.error('Dictionary refresh error:', error);
    throw error;
  } finally {
    client.release();
  }
}

// ==================== CALL HANDLERS ====================
export const callCompletedHandler: RequestHandler = catchAll(async (req, res) => {
  const { callSid } = req.query;

  if (callSid && typeof callSid === 'string') {
    const call = dialerState.activeCalls.get(callSid);
    if (call) {
      if (!dialerState.availableNumbers.includes(call.twilioNumber)) {
        dialerState.availableNumbers.push(call.twilioNumber);
      }
      dialerState.activeCalls.delete(callSid);

      if (dialerState.currentAgentCallSid === callSid) {
        dialerState.agentStatus = 'available';
        dialerState.currentAgentCallSid = null;
      }
    }
  }

  //processQueue();

  const twiml = new twilio.twiml.VoiceResponse();
  res.type('text/xml').send(twiml.toString());
});

export const agentCallHandler: RequestHandler = catchAll(async (req, res) => {
  const twiml = new twilio.twiml.VoiceResponse();

  const hasIncomingCall = Array.from(dialerState.activeCalls.values()).some(call =>
    call.status === 'dialing-agent' &&
    call.twilioNumber !== req.query.twilioNumber
  );

  if (!dialerState.canTakeCall() || hasIncomingCall) {
    logger.info('[AGENT] Rejected new call - agent already has an incoming call');
    twiml.say('Agent is currently receiving another call');
    twiml.hangup();
    res.type('text/xml').send(twiml.toString());
    return
  }

  const customerNumber = typeof req.query.customerNumber === 'string' ? req.query.customerNumber : undefined;
  const twilioNumber = typeof req.query.twilioNumber === 'string' ? req.query.twilioNumber : undefined;
  const leadId = typeof req.query.leadId === 'string' ? req.query.leadId : undefined;

  try {
    if (!customerNumber || !twilioNumber) {
      throw new Error('Missing customerNumber or twilioNumber');
    }

    dialerState.agentStatus = 'busy';
    dialerState.currentAgentCallSid = req.body.CallSid;

    const leadInfo = leadId ? leadsDictionary[leadId] : null;
    const callerName = leadInfo?.name || 'Customer';

    twiml.say(`Connecting you to ${callerName}, please wait.`);

    const dial = twiml.dial({
      callerId: twilioNumber,
      action: `${config.SERVER_URL}/handle-customer-dial-status?callSid=${req.body.CallSid}&customerName=${encodeURIComponent(callerName)}&leadId=${leadId || ''}`,
      timeout: config.CALL_TIMEOUT,
      record: 'record-from-answer'
    });

    dial.number({
      machineDetection: 'Enable',
      machineDetectionTimeout: 30,
      machineDetectionSpeechThreshold: 2400,
      machineDetectionSpeechEndThreshold: 1200,
      amdStatusCallback: `${config.SERVER_URL}/amd-status`
    }, customerNumber);

  } catch (error) {
    console.error('[AGENT CALL HANDLER] Error:', error);
    twiml.say('An error occurred connecting the call');
    twiml.hangup();

    dialerState.agentStatus = 'available';
    dialerState.currentAgentCallSid = null;
  }

  res.type('text/xml').send(twiml.toString());
});

export const handleCustomerDialStatus: RequestHandler = catchAll(async (req, res) => {
  const twiml = new twilio.twiml.VoiceResponse();
  const { callSid, customerName, leadId } = req.query;
  const { CallStatus, AnsweredBy, RecordingUrl, RecordingDuration } = req.body;

  logger.info('==================== CUSTOMER STATUS HANDLER ====================');

  const decodedCustomerName = typeof customerName === 'string' ?
    decodeURIComponent(customerName) : 'The customer';

  const isVoicemail = (
    (AnsweredBy === 'machine_start' ||
      AnsweredBy === 'machine' ||
      AnsweredBy === 'machine_end_beep' ||
      AnsweredBy === 'machine_end_silence' ||
      AnsweredBy === 'machine_end_other') ||
    (RecordingUrl && parseInt(RecordingDuration) > 0)
  );

  if (isVoicemail) {
    logger.info('==================== Playing voicemail message on line ====================');
    let customMessage = config.VOICEMAIL_MESSAGE;
    if (leadId && typeof leadId === 'string') {
      const leadInfo = leadsDictionary[leadId];
      if (leadInfo?.name) {
        customMessage = `Hello ${leadInfo.name}, ${config.VOICEMAIL_MESSAGE}`;
      }
    }

    twiml.pause({ length: 5 });
    twiml.say({
      voice: 'woman',
      language: 'en-US'
    }, customMessage);
    twiml.pause({ length: 1 });
  } else {
    switch (CallStatus) {
      case 'answered':
        twiml.say(`The call with ${decodedCustomerName} was disconnected.`);
        break;
      case 'no-answer':
        twiml.say(`${decodedCustomerName} is not answering their phone.`);
        break;
      case 'failed':
        twiml.say(`${decodedCustomerName}'s phone appears to be disconnected.`);
        break;
      case 'busy':
        twiml.say(`${decodedCustomerName}'s line is busy.`);
        break;
      default:
        twiml.say(`The call to ${decodedCustomerName} could not be completed.`);
    }

    twiml.pause({ length: 2 });
  }

  twiml.hangup();

  if (callSid && typeof callSid === 'string') {
    const call = dialerState.activeCalls.get(callSid);
    if (call) {
      if (!dialerState.availableNumbers.includes(call.twilioNumber)) {
        dialerState.availableNumbers.push(call.twilioNumber);
      }
      dialerState.activeCalls.delete(callSid);
    }
  }

  dialerState.agentStatus = 'available';
  dialerState.currentAgentCallSid = null;
  await processQueue();

  res.type('text/xml').send(twiml.toString());
});

export const amdStatusHandler: RequestHandler = catchAll(async (req, res) => {
  const twiml = new twilio.twiml.VoiceResponse();
  const callSid = typeof req.query.CallSid === 'string' ? req.query.CallSid : undefined;
  const leadId = typeof req.query.leadId === 'string' ? req.query.leadId : undefined;
  const { AnsweredBy, RecordingUrl, RecordingDuration } = req.body;

  logger.info('==================== AMD STATUS HANDLER ====================');
  logger.info('STATUS DATA:', JSON.stringify(req.body));

  const isVoicemail = (
    (AnsweredBy === 'machine_start' ||
      AnsweredBy === 'machine' ||
      AnsweredBy === 'machine_end_beep' ||
      AnsweredBy === 'machine_end_silence' ||
      AnsweredBy === 'machine_end_other') ||
    (RecordingUrl && parseInt(RecordingDuration) > 0)
  );

  if (isVoicemail) {
    logger.info('==================== Playing voicemail message ====================');

    let customMessage = config.VOICEMAIL_MESSAGE;
    if (leadId && leadsDictionary[leadId]?.name) {
      customMessage = `Hello ${leadsDictionary[leadId].name}, ${config.VOICEMAIL_MESSAGE}`;
    }

    twiml.pause({ length: 5 });
    twiml.say({
      voice: 'woman',
      language: 'en-US'
    }, customMessage);
    twiml.pause({ length: 1 });
    twiml.hangup();

    if (callSid) {
      const call = dialerState.activeCalls.get(callSid);
      if (call) {
        call.status = 'voicemail';
        call.answeredBy = 'machine';
        returnNumberToPool(call.twilioNumber);
      }
    }
  }

  res.type('text/xml').send(twiml.toString());
});

export const recordingStatusHandler: RequestHandler = catchAll(async (req, res) => {
  const { CallSid, RecordingUrl, RecordingDuration } = req.body;

  logger.info(`[RECORDING] Status for ${CallSid}:`, RecordingUrl ? 'Recording available' : 'No recording');

  if (RecordingUrl && parseInt(RecordingDuration) > 0) {
    const call = dialerState.activeCalls.get(CallSid);
    if (call) {
      call.answeredBy = 'machine';
      call.status = 'voicemail';
    }
  }

  res.sendStatus(200);
});

const processQueue = async (twilioNumber: string = config.TWILIO_NUMBERS[0]) => {

  logger.info('[DIALER] ####### PROCESS QUEUE ########');



  const lead = await getOneNewLead();
  if (!lead) {
    logger.info('[DIALER] No new leads available');
    return;
  }

  const customerNumber = lead.mobile || lead.phone;
  if (!customerNumber) {
    logger.info('[DIALER] Lead has no valid phone numbers');
    await updateZohoLeadStatus(lead.leadId, "invalid");
    await updateDBLeadStatus(lead.leadId, 'invalid');
    return;
  }

  // const twilioNumber = dialerState.availableNumbers.shift();
  if (!twilioNumber) {
    console.error('[DIALER] No available Twilio numbers');
    return;
  }

   logger.info(`PROCESS QUEUE:  1# Calling customer ${lead.name}, customer number ${customerNumber}, LeadId: ${lead.leadId} from twilioNumber ${twilioNumber} `);

  try {
    const customerCall = await client.calls.create({
      url: `${config.SERVER_URL}/customer-call-handler?twilioNumber=${encodeURIComponent(twilioNumber)}&leadId=${lead.leadId}&name=${encodeURIComponent(lead.name || '')}`,
      to: customerNumber,
      from: twilioNumber,
      timeout: config.CALL_TIMEOUT,
      machineDetection: 'DetectMessageEnd',
      machineDetectionTimeout: config.MACHINE_DETECTION_SENSITIVITY,
      statusCallback: `${config.SERVER_URL}/call-status?leadId=${encodeURIComponent(lead.leadId)}&name=${encodeURIComponent(lead.name || '')}`,
      statusCallbackEvent: ['initiated', 'ringing', 'answered', 'completed']
    });

    dialerState.activeCalls.set(customerCall.sid, {
      twilioNumber,
      customerNumber,
      status: 'dialing',
      leadId: lead.leadId
    });

  } catch (error) {
    console.error('[DIALER] Customer call initiation failed:', error);
    dialerState.availableNumbers.push(twilioNumber);

    await updateZohoLeadStatus(lead.leadId, "failed");
    await updateDBLeadStatus(lead.leadId, 'failed');
  }
};

export const customerCallHandler: RequestHandler = catchAll(async (req, res) => {
  const twiml = new twilio.twiml.VoiceResponse();
  const { CallStatus, AnsweredBy, To } = req.body;

  const getStringParam = (param): string | undefined => {
    if (typeof param === 'string') return param;
    if (Array.isArray(param)) return param[0]?.toString();
    return undefined;
  };

  //logger.info(` ${new Date()} = ${To}`)

  const twilioNumber = getStringParam(req.query.twilioNumber);
  const leadId = getStringParam(req.query.leadId);
  const name = getStringParam(req.query.name);

  logger.info('########## CUSTOMER CALL HANDLER ##########');
  logger.info('Call Status:', CallStatus);
  logger.info('AnsweredBy:', AnsweredBy);

  if (!twilioNumber || !leadId || !name) {
    logger.info('Initial call setup - returning empty TwiML');

    const nextCallerNumber = dialerState.availableNumbers.find(e => e != twilioNumber)

    await updateZohoLeadStatus(leadId, "failed");
    await updateDBLeadStatus(leadId, 'failed');

    await processQueue(nextCallerNumber);
    res.sendStatus(200);
    return
  }

  try {
    if (AnsweredBy === 'machine_start' ||
      AnsweredBy === 'machine' ||
      AnsweredBy === 'machine_end_beep' ||
      AnsweredBy === 'machine_end_silence' ||
      AnsweredBy === 'machine_end_other') {
      
      logger.info(`CUSTOMER CALL HANDLER: 2# Leaving voicemail  to Customer ${name}, customer number ${To}, LeadId: ${leadId} from twilioNumber ${twilioNumber} `);
      twiml.pause({ length: 3 });
      twiml.play(config.VOICEMAIL_MESSAGE);
      twiml.hangup();

      const nextCallerNumber = dialerState.availableNumbers.find(e => e != twilioNumber);
      
        await updateZohoLeadStatus(leadId, "voicemail");
        await updateDBLeadStatus(leadId, 'voicemail');
      

      
      // Delay before queue processing
      await processQueue(nextCallerNumber);
      logger.info(`calling the next nextCallerNumber after voice mail drop and updating zoho and database for  customer ${name}, number ${To}, lead ${leadId} from twilio number: ${twilioNumber} `);
      twiml.pause({ length: 3 });

      // we next available twilio num
    }
    else if (AnsweredBy === "unknown" || AnsweredBy === 'human') {
      //if (dialerState.canTakeCall()) {
      logger.info('########## CONNECTING TO THE AGENT ##########');
      logger.info(`Connecting Customer to agent:   ${name}, customer number ${To}, LeadId: ${leadId} from twilioNumber ${twilioNumber} `);
      const suffix = '@' + To;
      const rawName = name;
      const callerName = typeof rawName === 'string'
        ? rawName.replace(/\s+/g, '-')
        : 'Default-Caller';

      logger.info(`********************** Customer name ${callerName.replace(/\s+/g, "-") + suffix}`);

      const dial = twiml.dial({
        callerId: callerName.replace(/\s+/g, "-")
      });

      dial.sip(config.AGENT_SIP);

      await updateZohoLeadStatus(leadId, "success");
      await updateDBLeadStatus(leadId, 'success');  

    }
    // else {
    //   const nextCallerNumber = dialerState.availableNumbers.find(e => e != twilioNumber)
    //   processQueue(nextCallerNumber);
    //   logger.info(`CUSTOMER CALL HANDLER: Called regardless nextCallerNumber Customer ${name}, customer number ${To}, LeadId: ${leadId} from twilioNumber ${twilioNumber} `);
  
    // }

    res.type('text/xml').send(twiml.toString());
    return;

  } catch (error) {
    console.error('[CUSTOMER CALL HANDLER] Error:', error);

    const errorTwiml = new twilio.twiml.VoiceResponse();
    errorTwiml.say('An error occurred connecting your call');
    errorTwiml.hangup();

    await updateZohoLeadStatus(leadId, "failed");
    await updateDBLeadStatus(leadId, 'success');
    dialerState.agentStatus = 'available';
    dialerState.currentAgentCallSid = null;

    res.type('text/xml').send(errorTwiml.toString());
    return;
  }
   finally
   {
    const nextCallerNumber = dialerState.availableNumbers.find(e => e != twilioNumber)
    await processQueue(nextCallerNumber);

   }
});

export async function getOneNewLead() {
  const client = await db.connect();

  try {
    const result = await client.query<{
      lead_id: string;
      full_name: string;
      mobile: string | null;
      phone: string | null;
    }>(`
      SELECT l.lead_id, l.full_name, l.mobile, l.phone
      FROM leads l
      JOIN lead_status ls ON l.status_id = ls.status_id
      WHERE ls.status_name = 'new'
      LIMIT 1
    `);

    if (result.rows.length === 0) {
      return null;
    }

    const lead = result.rows[0];
    return {
      leadId: lead.lead_id,
      name: lead.full_name,
      mobile: lead.mobile,
      phone: lead.phone
    };
  } catch (error) {
    console.error('Error fetching new lead:', error);
    throw error;
  } finally {
    client.release();
  }
}

export const callStatusHandler: RequestHandler = catchAll(async (req, res) => {
  const { CallSid, CallStatus, To, From, Direction, AnsweredBy } = req.body;
  const { leadId, name } = req.query;

  logger.info(`##################CALL STATUS HANDLER #################################`);
  logger.info(`[CALL STATUS] Update for ${CallSid}: ${CallStatus}  To ${To}`);
  logger.info('CALL STATUS Lead  ID :' + leadId);

  const call = dialerState.activeCalls.get(CallSid);
  if (!call) {
    logger.info(`Call ${CallSid} not found in active calls`);
    res.sendStatus(200);
    return
  }

  call.status = CallStatus.toLowerCase() as CallStatus;
  

  switch (CallStatus.toLowerCase()) {
    case 'in-progress':
      if (Direction === 'outbound-api' && To !== config.AGENT_SIP) {
        logger.info(`Customer ${From} answered the call`);
        call.answeredBy = AnsweredBy === 'human' ? 'human' : 'machine';
      }
      break;

    case 'no-answer':
    case 'failed':
    case 'busy':
      logger.info(`Call to ${From} failed with status: ${CallStatus}`);
   
      dialerState.activeCalls.delete(CallSid);
      await updateZohoLeadStatus(leadId, CallStatus);
      await updateDBLeadStatus(leadId, 'no-answer');

      break;
    case 'completed':
      logger.info(`Call ${CallSid} completed`);
      logger.info(`Call AnsweredBy ${AnsweredBy} completed`);

      if (AnsweredBy === "unknown" || AnsweredBy === 'human') {
        dialerState.activeCalls.delete(CallSid);
        const nextCallerNumber = dialerState.availableNumbers.find(e => e != From)
        logger.info(`CALL STATUS HANDLER: CALLED CUSTOMER AFTER COMPLETION nextCallerNumber: Customer: ${name} customer number ${To}, LeadId: ${leadId} from twilioNumber ${nextCallerNumber} `);
        await processQueue(nextCallerNumber)
      }

      else if (!AnsweredBy)
      {

        await updateZohoLeadStatus(leadId, 'callback-pending');
        await updateDBLeadStatus(leadId, 'callback-pending');
        const nextCallerNumber = dialerState.availableNumbers.find(e => e != From)
        await processQueue(nextCallerNumber)
      }
      dialerState.activeCalls.delete(CallSid);
      dialerState.agentStatus = 'available';
      dialerState.currentAgentCallSid = null;
      break;


  }


 


  res.sendStatus(200);
});

export const updateZohoLeadStatusHandler: RequestHandler = catchAll(async (req, res) => {
  const { leadId, lead_status } = req.body;

  logger.info(` updateZohoLeadStatusHandler [ZOHO] Updating lead ${leadId} status to:`, lead_status);

  if (!leadId || !lead_status) {
    console.warn('[ZOHO] Missing required fields for lead update');
    res.status(400).json({
      success: false,
      message: 'Missing required fields: leadId and lead_status'
    });
    return;
  }

  try {
    await updateZohoLeadStatus(leadId, lead_status)
    logger.info(`[ZOHO] Successfully updated lead ${leadId} status`);
    res.json({
      success: true,
      message: 'Lead status updated successfully'
    });
  } catch (error) {
    console.error('[ZOHO] Error updating lead status:', error);
    throw error;
  }
});

const updateZohoLeadStatus = async (leadId, lead_status) => {
  logger.info(`updating Zoho Lead Field **Call_Outcome_or_Conversion** to : ${lead_status} with Lead_ID ${leadId}`);

  const headers = await ZohoAuth.getHeaders();

  switch (lead_status) {
    case 'no-answer':
      lead_status = "No Answer - Auto Dialler"
      break;
    case 'failed':
      lead_status = "Failed Call - Auto Dialler"
      break;
    case 'success':
      lead_status = "Successful Call - Auto Dialler"
      break;
    case 'voicemail':
      lead_status = "No Answer VM Left - Auto Dialler"
      break;
    case 'invalid':
      lead_status = "Failed Invalid Number - Auto Dialler"
      break;
    case 'callback-pending':
      lead_status = "Callback Pending - Auto Dialler"
      break;
    default:
  }

  const dataZoho = {
    data: [
      {
        Call_Outcome_or_Conversion: lead_status
      },
    ],
  };

  await axios.put(`${zohoLeadURL}/${leadId}`, dataZoho, { headers });
}

export async function updateDBLeadStatus(leadId, newStatus) {
  const VALID_STATUSES = ['new', 'no-answer', 'voicemail', 'success', 'failed', 'invalid', 'callback-pending'];

  if (!VALID_STATUSES.includes(newStatus)) {
    return {
      success: false,
      error: `Invalid status. Valid values are: ${VALID_STATUSES.join(', ')}`
    };
  }

  const client = await db.connect();

  try {
    await client.query('BEGIN');

    const leadCheck = await client.query(
      `SELECT lead_id, status 
       FROM leads 
       WHERE lead_id = $1 
       FOR UPDATE LIMIT 1`,
      [leadId]
    );

    if (leadCheck.rowCount === 0) {
      await client.query('ROLLBACK');
      return {
        success: false,
        error: `Lead ${leadId} not found`
      };
    }

    const currentStatus = leadCheck.rows[0].status;

    if (currentStatus === newStatus) {
      await client.query('ROLLBACK');
      return {
        success: false,
        error: `Lead already has status ${newStatus}`,
        lead: leadCheck.rows[0]
      };
    }

    const updateResult = await client.query(
      `UPDATE leads 
       SET status = $1,
           status_id = (SELECT status_id FROM lead_status WHERE status_name = $1 LIMIT 1),
           updated_at = NOW()
       WHERE lead_id = $2
       RETURNING lead_id, full_name, mobile, phone, status, status_id, updated_at`,
      [newStatus, leadId]
    );

    await client.query('COMMIT');
    logger.info(`########## updated lead ${leadId} ##########  ${updateResult.rows[0]}`);
    return {
      success: true,
      lead: updateResult.rows[0]
    };

  } catch (error) {
    await client.query('ROLLBACK');
    console.error(`Database error updating lead ${leadId}:`, error);

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Database operation failed'
    };
  } finally {
    client.release();
  }
}