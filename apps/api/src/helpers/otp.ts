import moment from 'moment';
import { ZohoA<PERSON>, zoho<PERSON>eadURL } from './zoho';
import axios from 'axios';
export default class OtpHelper {
  static async generateOtp(client, phoneNumber: string, length: number = 4, reset : boolean = false) {
    const characters: string = '0123456789';
    let otp: string = '';

    for (let i = 0; i < length; i++) {
      otp += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    await client.query(
      `INSERT INTO otps
    ("otp", "expirationdate", "phone","createdat", "updatedat","context")
    VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,$4)`,
      [otp, moment().add(30, 'minute'), phoneNumber,reset ? "resetpassword":"register"],
    );
    return otp;
  }

  static async disablePhoneOtp(client, phoneNumber: string) {
    await client.query(`UPDATE otps set "active"=$1 WHERE phone=$2`, [false, phoneNumber]);
  }

  static sendOTP(phoneNumber: string, otp: string, leadId: string): void {
    console.log(`OTP sent to ${phoneNumber} : ${otp}`);

     phoneNumber = phoneNumber.replace(/\s/g, "");

    const postData = {
      messages: [
        {
          source: 'php',
          body: `Your Zenith Clinic OTP code is ${otp}`,
          from: 'zenith',
          to: phoneNumber,
        },
      ],
    };

    try {
      fetch('https://rest.clicksend.com/v3/sms/send', {
        method: 'POST',
        body: JSON.stringify(postData),
        headers: {
          Authorization: 'Basic dmFsc2F1c3RyYWxpYToxNjJCNzlCMC03MUZGLUZFMjItRjEzMC1CRENGNDAzNEU3RUU=',
          'Content-Type': 'application/json',
        },
      }).then(async (response) => {
        const responseBody = await response.json();
        const responseCode = response.status;

        if (responseCode !== 200 || responseBody.http_code !== 200 || responseBody.response_code !== 'SUCCESS') {
          const data = {
            data: [
              {
                OTP_Status: 'OTP failed : ' + JSON.stringify(responseBody),
              },
            ],
          };
          const headers = await ZohoAuth.getHeaders();
          await axios.put(`${zohoLeadURL}/${leadId}`, data, { headers });
          console.log(`Sending OTP error: ${JSON.stringify(responseBody)}`);
        }
      }).catch(async(error)=>{
        let message = '';
        if (error instanceof Error) {
          message = error.message;
          console.log(`Sending OTP error: ${message}`);
          const data = {
            data: [
              {
                OTP_Status: 'OTP failed : ' + message,
              },
            ],
          };
          const headers = await ZohoAuth.getHeaders();
          await axios.put(`${zohoLeadURL}/${leadId}`, data, { headers });
        }else{
          console.log('An unknown error occurred ' + error);
        }
      });
    } catch (error) {
      console.log('An unknown error occurred '+error);
    }
  }
}
