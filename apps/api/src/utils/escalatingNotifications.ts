import { db } from '../utils/db';
import { getPatientsInCurrentActiveSlot } from './timeSlotUtils';
import { ZohoAuth } from '../helpers/zoho';
import { zohoLeadURL } from '../helpers/zoho';
import axios from 'axios';
import { logger } from '../config/logger';
import config from '../config';

/**
 * Interface for escalation candidate data
 */
interface EscalationCandidate {
  patientID: string;
  fullName: string;
  zohoID: string;
  notificationSentDateTime: string;
  minutesSinceNotification: number;
  consultationDate: string;
}

/**
 * Interface for Zoho API headers
 */
interface ZohoHeaders {
  Authorization: string;
  'Content-Type': string;
  [key: string]: string;
}



/**
 * Send escalating notifications to patients who haven't come online
 * within their active consultation time ranges
 */
export const sendEscalatingNotifications = async (): Promise<void> => {
  // Check if escalating notifications are enabled
  if (!config.escalatingNotifications.enabled) {
    logger.info('Escalating notifications are disabled via configuration');
    return;
  }

  const client = await db.connect();

  try {
    await client.query('BEGIN');
    
    // Step 1: Get patients in currently active consultation slots
    const activeSlotData = await getPatientsInCurrentActiveSlot(db);
    const { patientIDs: activePatientIDs } = activeSlotData;
    
    if (activePatientIDs.length === 0) {
      logger.info('No patients in currently active consultation slots for escalation');
      await client.query('COMMIT');
      return;
    }
    
    // Step 2: Find escalation candidates (notified but not online, configurable minutes elapsed)
    const firstWarningMinutes = config.escalatingNotifications.firstWarningMinutes;
    const escalationQuery = `
      SELECT
        p."patientID",
        p."fullName",
        p."zohoID",
        pq."notificationSentDateTime",
        EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - pq."notificationSentDateTime"))/60 as minutes_since_notification,
        c."consultationDate"
      FROM PatientQueue pq
      JOIN Patient p ON p."patientID" = pq."patientID"
      JOIN Consultation c ON c."patientID" = pq."patientID"
      WHERE pq."notificationSentDateTime" IS NOT NULL
        AND pq."joinedAt" IS NULL
        AND pq.status = 'OFFLINE'
        AND CURRENT_TIMESTAMP >= pq."notificationSentDateTime" + INTERVAL '${firstWarningMinutes} minutes'
        AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
        AND c.completed = FALSE
      ORDER BY pq."notificationSentDateTime" ASC
    `;
    
    const escalationResult = await client.query(escalationQuery);
    const allCandidates = escalationResult.rows as EscalationCandidate[];
    
    // Step 3: Filter candidates to only include those in active consultation slots
    const activeCandidates = allCandidates.filter(candidate => 
      activePatientIDs.includes(candidate.patientID)
    );
    
    if (activeCandidates.length === 0) {
      logger.info('No escalation candidates found in active consultation slots');
      await client.query('COMMIT');
      return;
    }
    
    //logger.info(`Found ${activeCandidates.length} escalation candidates in active slots`);
    
    // Step 4: Get Zoho headers for API calls
    const headers = await ZohoAuth.getHeaders();
    
    // Step 5: Process each candidate for escalation
    for (const candidate of activeCandidates) {
      try {
        await processEscalationCandidate(candidate, headers);
        
        // Add delay between notifications to avoid overwhelming Zoho API
        await delay(2000); // 2 second delay (following callNoShowPatient pattern)
        
      } catch (error) {
        const err = error as Error;
        logger.error(`Error processing escalation for patient ${candidate.fullName}: ${err.message}`);
        // Continue with other patients even if one fails
      }
    }
    
    await client.query('COMMIT');
    
  } catch (error) {
    await client.query('ROLLBACK');
    const err = error as Error;
    logger.error(`Error in sendEscalatingNotifications: ${err.message}`);
    throw error;
  } finally {
    client.release();
  }
};

/**
 * Process escalation for a single candidate
 */
async function processEscalationCandidate(
  candidate: EscalationCandidate,
  headers: ZohoHeaders
): Promise<void> {
  const { fullName, zohoID, minutesSinceNotification } = candidate;

  // Get configurable escalation timing
  const firstWarningMinutes = config.escalatingNotifications.firstWarningMinutes;
  const secondWarningMinutes = config.escalatingNotifications.secondWarningMinutes;

  // Determine what escalation to send based on elapsed time and configuration
  if (minutesSinceNotification >= secondWarningMinutes) {
    // Send 2nd warning (configurable minutes)
    await sendSecondWarning(zohoID, headers);
    logger.info(`2nd warning sent to ${fullName} (${Math.round(minutesSinceNotification)} minutes since notification, threshold: ${secondWarningMinutes}min)`);

  } else if (minutesSinceNotification >= firstWarningMinutes) {
    // Send 1st warning (configurable minutes)
    await sendFirstWarning(zohoID, headers);
    logger.info(`1st warning sent to ${fullName} (${Math.round(minutesSinceNotification)} minutes since notification, threshold: ${firstWarningMinutes}min)`);
  }
}



/**
 * Send first warning notification via Zoho
 */
async function sendFirstWarning(zohoID: string, headers: ZohoHeaders): Promise<void> {
  const data = {
    data: [
      {
        'Queue_Warning_Messages': 'consult-follow-up-1st-warning-sms'
      }
    ]
  };

  await axios.put(`${zohoLeadURL}/${zohoID}`, data, { headers });
}

/**
 * Send second warning notification via Zoho
 */
async function sendSecondWarning(zohoID: string, headers: ZohoHeaders): Promise<void> {
  const data = {
    data: [
      {
        'Queue_Warning_Messages': 'consult-follow-up-2nd-warning-sms'
      }
    ]
  };

  await axios.put(`${zohoLeadURL}/${zohoID}`, data, { headers });
}

/**
 * Utility function to add delay between operations
 */
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}
