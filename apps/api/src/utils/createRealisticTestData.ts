// import { db } from './db';
// import { DateTime } from 'luxon';

// /**
//  * Creates realistic test data for Queue Ranges Management testing
//  * Uses actual doctors from the database and creates current-time-aware test scenarios
//  */

// interface Doctor {
//   id: string;
//   username: string;
//   email: string;
//   accessID: string;
//   initials: string;
// }

// interface TestRange {
//   id: string;
//   doctorID: string;
//   start: string;
//   end: string;
//   date: string;
// }

// interface TestPatient {
//   patientID: string;
//   fullName: string;
//   email: string;
//   zohoID: string;
// }

// async function createRealisticTestData(): Promise<void> {
//   console.log('🧪 CREATING REALISTIC TEST DATA FOR QUEUE RANGES MANAGEMENT');
//   console.log('================================================================================');
  
//   const client = await db.connect();
  
//   try {
//     // Get current Sydney time
//     const now = DateTime.now().setZone('Australia/Sydney');
//     const today = now.toFormat('yyyy-MM-dd');
//     const currentHour = now.hour;
//     const currentMinute = now.minute;
    
//     console.log(`📅 Current Sydney time: ${now.toFormat('h:mm a, dd MMM yyyy')}`);
//     console.log(`🎯 Creating test data for: ${today}`);
    
//     // Select 3 real doctors for testing
//     const doctors: Doctor[] = [
//       {
//         id: 'dd7e4893-edf4-4bbb-a8f1-0b639d41d4f8',
//         username: 'Joseph Mojoo',
//         email: '<EMAIL>',
//         accessID: 'ff2608a4-d87a-4538-ab0b-b4a21237cb4e',
//         initials: 'TJ'
//       },
//       {
//         id: '87fa818b-c3ce-4d4f-8401-6b8faf994799',
//         username: 'Dr. Gazale',
//         email: '<EMAIL>',
//         accessID: '3f6bcfc5-8a6a-44a2-8672-89e4d31ce74b',
//         initials: 'G'
//       },
//       {
//         id: 'ee9f7652-6763-4bcf-a03d-89892b69e51e',
//         username: 'AshleighW',
//         email: '<EMAIL>',
//         accessID: 'f686fbb1-5e7b-43c5-8a50-9b6f322cfaf5',
//         initials: 'AW'
//       }
//     ];
    
//     console.log(`\n👥 Using ${doctors.length} real doctors:`);
//     doctors.forEach(dr => {
//       console.log(`   🩺 ${dr.username} (${dr.email})`);
//     });
    
//     // Create realistic time ranges based on current time
//     const ranges: TestRange[] = [];
//     let rangeCounter = 1;
    
//     // Create ranges that include current time and future slots
//     const baseHour = Math.max(9, currentHour - 1); // Start from 9 AM or 1 hour before current time
    
//     for (let i = 0; i < doctors.length; i++) {
//       const doctor = doctors[i];
      
//       // Create 4 time slots per doctor (2 hours of consultations)
//       for (let slotIndex = 0; slotIndex < 4; slotIndex++) {
//         const slotHour = baseHour + slotIndex;
//         const startTime = `${slotHour.toString().padStart(2, '0')}:00:00`;
//         const endTime = `${slotHour.toString().padStart(2, '0')}:30:00`;
        
//         const range: TestRange = {
//           id: `REALISTIC_RANGE_${rangeCounter.toString().padStart(3, '0')}`,
//           doctorID: doctor.id,
//           start: startTime,
//           end: endTime,
//           date: today
//         };
        
//         ranges.push(range);
//         rangeCounter++;
//       }
//     }
    
//     console.log(`\n📅 Creating ${ranges.length} realistic time ranges:`);
//     ranges.forEach(range => {
//       const doctor = doctors.find(d => d.id === range.doctorID);
//       const isActive = isTimeSlotActive(range.start, range.end, now);
//       console.log(`   ${isActive ? '🔥' : '⏳'} ${doctor?.username}: ${range.start.slice(0, 5)} - ${range.end.slice(0, 5)} ${isActive ? '(ACTIVE NOW)' : ''}`);
//     });
    
//     // Clean up existing test data (only if it exists)
//     console.log('\n🧹 Cleaning up any existing REALISTIC test data...');
//     const existingData = await client.query(`SELECT COUNT(*) as count FROM "Patient" WHERE "patientID" LIKE 'REALISTIC_%'`);
//     if (parseInt(existingData.rows[0].count) > 0) {
//       await client.query(`DELETE FROM "PatientQueue" WHERE "patientID" LIKE 'REALISTIC_%'`);
//       await client.query(`DELETE FROM "PatientSlot" WHERE patient_id LIKE 'REALISTIC_%'`);
//       await client.query(`DELETE FROM "Consultation" WHERE "patientID" LIKE 'REALISTIC_%'`);
//       await client.query(`DELETE FROM "Patient" WHERE "patientID" LIKE 'REALISTIC_%'`);
//       await client.query(`DELETE FROM "Range" WHERE id LIKE 'REALISTIC_%'`);
//       console.log('   ✅ Cleaned up existing test data');
//     } else {
//       console.log('   ℹ️  No existing test data to clean up');
//     }
    
//     // Insert ranges
//     console.log('\n📊 Inserting ranges...');
//     for (const range of ranges) {
//       await client.query(`
//         INSERT INTO "Range" (
//           id, day, date, start, "end", interval, availability, status, 
//           "createdAt", "updatedAt", "noShowAvailability", "doctorID"
//         ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
//       `, [
//         range.id,
//         now.toFormat('cccc'), // Day name
//         range.date,
//         range.start,
//         range.end,
//         30, // 30-minute intervals
//         10, // 10 patients per slot
//         'active',
//         now.toJSDate(),
//         now.toJSDate(),
//         2, // 2 no-show slots
//         range.doctorID
//       ]);
//     }
    
//     console.log(`   ✅ Created ${ranges.length} ranges`);
    
//     // Create realistic patients
//     console.log('\n👥 Creating realistic patients...');
//     const patients: TestPatient[] = [];
    
//     for (let i = 1; i <= 30; i++) {
//       const patient: TestPatient = {
//         patientID: `REALISTIC_PATIENT_${i.toString().padStart(3, '0')}`,
//         fullName: `Test Patient ${i}`,
//         email: `realistic.patient${i}@test.com`,
//         zohoID: `ZOHO_${i.toString().padStart(6, '0')}`
//       };
//       patients.push(patient);
//     }
    
//     // Insert patients
//     for (const patient of patients) {
//       await client.query(`
//         INSERT INTO "Patient" (
//           "patientID", "fullName", email, "zohoID", "createdAt", "updatedAt"
//         ) VALUES ($1, $2, $3, $4, $5, $6)
//       `, [
//         patient.patientID,
//         patient.fullName,
//         patient.email,
//         patient.zohoID,
//         now.toJSDate(),
//         now.toJSDate()
//       ]);
//     }
    
//     console.log(`   ✅ Created ${patients.length} patients`);
    
//     // Create patient slots (assign patients to ranges)
//     console.log('\n🎯 Assigning patients to time slots...');
//     let patientIndex = 0;
//     let slotCounter = 1;
    
//     for (const range of ranges) {
//       const doctor = doctors.find(d => d.id === range.doctorID);
//       const patientsPerSlot = 3; // 3 patients per slot for testing
      
//       console.log(`   📋 ${doctor?.username} (${range.start.slice(0, 5)} - ${range.end.slice(0, 5)}):`);
      
//       for (let i = 0; i < patientsPerSlot; i++) {
//         if (patientIndex >= patients.length) break;
        
//         const patient = patients[patientIndex];
        
//         // Insert PatientSlot
//         await client.query(`
//           INSERT INTO "PatientSlot" (
//             patient_id, range_id, slot_id, "createdAt", "updatedAt", 
//             "sameDayRebook", "queueType", "bookingType"
//           ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
//         `, [
//           patient.patientID,
//           range.id,
//           slotCounter,
//           now.toJSDate(),
//           now.toJSDate(),
//           false,
//           'regular',
//           'online'
//         ]);
        
//         // Create consultation
//         const consultationTime = DateTime.fromFormat(`${today} ${range.start}`, 'yyyy-MM-dd HH:mm:ss', { zone: 'Australia/Sydney' });
        
//         await client.query(`
//           INSERT INTO "Consultation" (
//             "patientID", "consultationDate", "notificationSent", completed,
//             "createdAt", "updatedAt", "drId"
//           ) VALUES ($1, $2, $3, $4, $5, $6, $7)
//         `, [
//           patient.patientID,
//           consultationTime.toUTC().toJSDate(),
//           false,
//           false,
//           now.toJSDate(),
//           now.toJSDate(),
//           doctor?.accessID
//         ]);
        
//         // Add patient to PatientQueue for notifications
//         await client.query(`
//           INSERT INTO "PatientQueue" (
//             "patientID", email, "fullName", "assignedDoctorID", status,
//             "createdAt", "updatedAt", "notificationSent", "joinedAt", "completedAt"
//           ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
//         `, [
//           patient.patientID,
//           patient.email,
//           patient.fullName,
//           doctor?.accessID,
//           'OFFLINE', // Start as OFFLINE so they can be notified
//           now.toJSDate(),
//           now.toJSDate(),
//           false, // Not yet notified
//           null, // Not yet joined
//           null  // Not yet completed
//         ]);

//         console.log(`      👤 ${patient.patientID} → ${consultationTime.toFormat('h:mm a')} (queued for notifications)`);

//         patientIndex++;
//         slotCounter++;
//       }
//     }
    
//     console.log('\n🎉 REALISTIC TEST DATA CREATED SUCCESSFULLY!');
//     console.log('\n📊 SUMMARY:');
//     console.log(`   🩺 Doctors: ${doctors.length}`);
//     console.log(`   📅 Time ranges: ${ranges.length}`);
//     console.log(`   👥 Patients: ${patients.length}`);
//     console.log(`   🎯 Patient slots: ${patientIndex}`);
    
//     const activeRanges = ranges.filter(r => isTimeSlotActive(r.start, r.end, now));
//     console.log(`   🔥 Currently active slots: ${activeRanges.length}`);
    
//     if (activeRanges.length > 0) {
//       console.log('\n🔥 ACTIVE SLOTS RIGHT NOW:');
//       activeRanges.forEach(range => {
//         const doctor = doctors.find(d => d.id === range.doctorID);
//         console.log(`   ✅ ${doctor?.username}: ${range.start.slice(0, 5)} - ${range.end.slice(0, 5)}`);
//       });
//       console.log('\n🚀 READY FOR REAL TESTING!');
//       console.log('\n📋 TO TEST REAL NOTIFICATIONS:');
//       console.log('   1. Start doctor timers for the doctors above');
//       console.log('   2. The system will automatically detect active slots and send real notifications');
//       console.log('   3. Check PatientQueue table for notification updates');
//       console.log('   4. Monitor Zoho API calls for actual notification sending');
//       console.log('\n🔍 MONITORING COMMANDS:');
//       console.log('   • Check active slot detection: npx tsx src/utils/testSimplifiedNotifications.ts');
//       console.log('   • Monitor PatientQueue: SELECT * FROM "PatientQueue" WHERE "patientID" LIKE \'REALISTIC_%\';');
//       console.log('   • Check consultations: SELECT * FROM "Consultation" WHERE "patientID" LIKE \'REALISTIC_%\';');
//     } else {
//       console.log('\n⏳ No currently active slots - notifications will be sent when the next slot becomes active.');
//       console.log('\n📅 UPCOMING SLOTS:');
//       ranges.slice(0, 3).forEach(range => {
//         const doctor = doctors.find(d => d.id === range.doctorID);
//         console.log(`   ⏰ ${doctor?.username}: ${range.start.slice(0, 5)} - ${range.end.slice(0, 5)}`);
//       });
//     }

//     console.log('\n⚠️  IMPORTANT: This data will NOT be automatically deleted.');
//     console.log('   Use this for real testing with actual notification sending.');
//     console.log('   Data will persist until manually cleaned up.');
    
//   } catch (error) {
//     console.error('❌ Error creating test data:', error);
//     throw error;
//   } finally {
//     client.release();
//   }
// }

// function isTimeSlotActive(startTime: string, endTime: string, currentTime: DateTime): boolean {
//   const today = currentTime.toFormat('yyyy-MM-dd');
//   const slotStart = DateTime.fromFormat(`${today} ${startTime}`, 'yyyy-MM-dd HH:mm:ss', { zone: 'Australia/Sydney' });
//   const slotEnd = DateTime.fromFormat(`${today} ${endTime}`, 'yyyy-MM-dd HH:mm:ss', { zone: 'Australia/Sydney' });
  
//   return currentTime >= slotStart && currentTime < slotEnd;
// }

// // Run the script
// if (require.main === module) {
//   createRealisticTestData()
//     .then(() => {
//       console.log('\n✅ Script completed successfully!');
//       process.exit(0);
//     })
//     .catch((error) => {
//       console.error('\n❌ Script failed:', error);
//       process.exit(1);
//     });
// }

// export { createRealisticTestData };
