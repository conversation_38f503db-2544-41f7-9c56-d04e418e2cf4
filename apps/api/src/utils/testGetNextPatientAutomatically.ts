import { db } from '../utils/db';
import { getPatientsInCurrentActiveSlot } from '../utils/timeSlotUtils';
import { DateTime } from 'luxon';
import axios from 'axios';

// Handle EPIPE errors gracefully
process.stdout.on('error', (err: NodeJS.ErrnoException) => {
  if (err.code === 'EPIPE') {
    process.exit(0);
  }
});

interface TestDoctor {
  accessID: string;
  username: string;
  id: string;
}

const TEST_DOCTORS: TestDoctor[] = [
  { accessID: 'range-test-dr-1', username: 'TEST_Dr_Range_1', id: '' },
  { accessID: 'range-test-dr-2', username: 'TEST_Dr_Range_2', id: '' },
  { accessID: 'range-test-dr-3', username: 'TEST_Dr_Range_3', id: '' }
];

async function createRangeAwareTestData(): Promise<void> {
  console.log('\n🔧 CREATING RANGE-AWARE TEST DATA FOR getNextPatientAutomatically');
  
  const client = await db.connect();
  try {
    await client.query('BEGIN');

    // Clean up any existing test data
    await client.query(`DELETE FROM patientqueue WHERE "patientID" LIKE 'RANGE_TEST_%'`);
    await client.query(`DELETE FROM consultation WHERE "patientID" LIKE 'RANGE_TEST_%'`);
    await client.query(`DELETE FROM patient WHERE "patientID" LIKE 'RANGE_TEST_%'`);
    await client.query(`DELETE FROM patientslot WHERE patient_id LIKE 'RANGE_TEST_%'`);
    await client.query(`DELETE FROM slot WHERE range_id LIKE 'range-test-%'`);
    await client.query(`DELETE FROM range WHERE id LIKE 'range-test-%'`);
    await client.query(`DELETE FROM dr WHERE "accessID" LIKE 'range-test-dr-%'`);

    // Create test doctors and get their IDs
    for (let i = 0; i < TEST_DOCTORS.length; i++) {
      const doctor = TEST_DOCTORS[i];
      const result = await client.query(`
        INSERT INTO dr ("accessID", username, email, "createdAt", "updatedAt")
        VALUES ($1, $2, $3, NOW(), NOW())
        ON CONFLICT ("accessID") DO UPDATE SET
          username = EXCLUDED.username,
          "updatedAt" = NOW()
        RETURNING id
      `, [doctor.accessID, doctor.username, `${doctor.username}@test.com`]);
      
      TEST_DOCTORS[i].id = result.rows[0].id;
      console.log(`   ✅ Created doctor: ${doctor.username} (ID: ${doctor.id})`);
    }

    // Create realistic time ranges for today - current active slot
    const now = DateTime.now().setZone('Australia/Sydney');
    const currentMinute = now.minute;
    let currentSlotStart: DateTime;

    if (currentMinute < 30) {
      currentSlotStart = now.set({ minute: 0, second: 0, millisecond: 0 });
    } else {
      currentSlotStart = now.set({ minute: 30, second: 0, millisecond: 0 });
    }

    const slotStart = currentSlotStart;
    const slotEnd = slotStart.plus({ minutes: 30 });

    // Create ranges and patients for each doctor
    for (let i = 0; i < TEST_DOCTORS.length; i++) {
      const doctor = TEST_DOCTORS[i];
      const rangeId = `range-test-${i + 1}`;
      
      // Create range for this doctor
      await client.query(`
        INSERT INTO range (id, day, date, start, "end", interval, availability, status, "doctorID", "createdAt", "updatedAt")
        VALUES ($1, $2, $3, $4, $5, $6, $7, 'active', $8, NOW(), NOW())
      `, [
        rangeId,
        now.toFormat('cccc').toLowerCase(),
        now.toFormat('yyyy-MM-dd'),
        slotStart.toFormat('HH:mm'),
        slotEnd.toFormat('HH:mm'),
        30,
        20,
        doctor.id
      ]);
      
      // Create a slot for this range
      const slotResult = await client.query(`
        INSERT INTO slot (range_id, slot, remaining, "createdAt", "updatedAt", "noShowRemaining")
        VALUES ($1, $2, $3, NOW(), NOW(), $4)
        RETURNING id
      `, [
        rangeId,
        `${slotStart.toFormat('HH:mm')} - ${slotEnd.toFormat('HH:mm')}`,
        20,
        20
      ]);

      const slotId = slotResult.rows[0].id;

      // Create patients for this doctor
      const patientsPerDoctor = 5;
      for (let j = 0; j < patientsPerDoctor; j++) {
        const patientCounter = i * patientsPerDoctor + j + 1;
        const patientID = `RANGE_TEST_${patientCounter.toString().padStart(3, '0')}`;
        const zohoID = `RANGE_TEST_ZOHO_${patientCounter.toString().padStart(3, '0')}`;
        
        // Create patient
        await client.query(`
          INSERT INTO patient ("patientID", "fullName", "email", "mobile", "zohoID", "returningPatient", "riskRating", "createdAt", "updatedAt")
          VALUES ($1, $2, $3, '**********', $4, $5, $6, NOW(), NOW())
          ON CONFLICT ("patientID") DO NOTHING
        `, [
          patientID, 
          `Range Test Patient ${patientCounter}`, 
          `${patientID}@test.com`,
          zohoID,
          Math.random() > 0.7,
          Math.floor(Math.random() * 5) + 1
        ]);
        
        // Assign patient to doctor's range via patientslot
        await client.query(`
          INSERT INTO patientslot (patient_id, range_id, slot_id, "createdAt", "updatedAt")
          VALUES ($1, $2, $3, NOW(), NOW())
        `, [zohoID, rangeId, slotId]);
        
        // Create consultation for this patient at the shared slot time
        await client.query(`
          INSERT INTO consultation ("patientID", "consultationDate", "notificationSent", completed, "createdAt", "updatedAt")
          VALUES ($1, $2, false, false, NOW(), NOW())
          ON CONFLICT ("patientID", "consultationDate") DO UPDATE SET
            "notificationSent" = false,
            completed = false,
            "updatedAt" = NOW()
        `, [patientID, slotStart.toJSDate()]);

        // Create patient queue entries (some online, some not)
        const isOnline = j < 3; // First 3 patients are online
        const status = isOnline ? (j === 0 ? 'ONLINE' : 'JOINED') : 'OFFLINE';
        
        await client.query(`
          INSERT INTO patientqueue ("patientID", "email", status, "notificationSentDateTime", "createdAt", "updatedAt")
          VALUES ($1, $2, $3, NOW(), NOW(), NOW())
          ON CONFLICT ("patientID") DO UPDATE SET
            status = EXCLUDED.status,
            "notificationSentDateTime" = EXCLUDED."notificationSentDateTime",
            "updatedAt" = NOW()
        `, [patientID, `${patientID}@test.com`, status]);
      }

      console.log(`   📅 Created range for ${doctor.username}: ${slotStart.toFormat('h:mm a')} - ${slotEnd.toFormat('h:mm a')} with ${patientsPerDoctor} patients`);
    }

    await client.query('COMMIT');
    console.log(`\n   ✅ Created ${TEST_DOCTORS.length} test doctors with ranges and patients`);

  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

async function testActiveSlotDetection(): Promise<void> {
  console.log('\n🔍 TESTING ACTIVE SLOT DETECTION FOR getNextPatientAutomatically');

  const activeSlotData = await getPatientsInCurrentActiveSlot(db);
  const { patientIDs: activePatientIDs, activeSlotInfo, detectedInterval } = activeSlotData;

  console.log(`\n📊 ACTIVE SLOT DETECTION RESULTS:`);
  console.log(`   📅 Detected slot time: ${activeSlotInfo?.slotTime.toFormat('h:mm:ss a') || 'None'}`);
  console.log(`   ⏱️  Detected interval: ${detectedInterval} minutes`);
  console.log(`   👥 Patients found: ${activePatientIDs.length}`);

  if (activePatientIDs.length > 0) {
    console.log(`   📋 Patient IDs in active slot: [${activePatientIDs.slice(0, 10).join(', ')}${activePatientIDs.length > 10 ? '...' : ''}]`);
  }
}

// Test configuration
const API_BASE_URL = 'http://localhost:3000'; // Adjust if your API runs on a different port

interface APITestResult {
  success: boolean;
  statusCode: number;
  data?: unknown;
  error?: string;
}

async function testGetNextPatientAutomaticallyAPI(doctorAccessID: string): Promise<APITestResult> {
  try {
    const response = await axios.get(`${API_BASE_URL}/doctor/getNextPatientAutomatically/${doctorAccessID}`, {
      timeout: 10000
    });
    return {
      success: true,
      statusCode: response.status,
      data: response.data
    };
  } catch (error) {
    const axiosError = error as { response?: { status?: number; data?: unknown }; message?: string };
    return {
      success: false,
      statusCode: axiosError.response?.status || 0,
      error: axiosError.message || 'Unknown error',
      data: axiosError.response?.data || null
    };
  }
}

async function testGetNextPatientAutomaticallyRangeAware(): Promise<void> {
  console.log('\n🧪 TESTING getNextPatientAutomatically WITH RANGE-AWARE LOGIC');
  console.log('=' .repeat(80));
  console.log(`   🌐 API Base URL: ${API_BASE_URL}`);
  console.log(`   ⚠️  Note: This test requires the API server to be running`);

  const results: Array<{
    doctor: string;
    hasActiveSlot: boolean;
    patientFound: boolean;
    patientID?: string;
    success: boolean;
    error?: string;
  }> = [];

  for (const doctor of TEST_DOCTORS) {
    console.log(`\n🩺 Testing getNextPatientAutomatically API for: ${doctor.username}`);
    console.log('-'.repeat(50));

    try {
      // Call the API endpoint
      const result = await testGetNextPatientAutomaticallyAPI(doctor.accessID);

      console.log(`   📊 Response status: ${result.statusCode}`);
      console.log(`   ✅ API call success: ${result.success}`);

      if (result.success) {
        const responseData = result.data as { data?: { patientID?: string; email?: string; status?: string; joinedCallAt?: string }; message?: string };
        const patientFound = responseData?.data?.patientID ? true : false;
        const patientID = responseData?.data?.patientID;

        if (patientFound) {
          console.log(`   ✅ Patient found: ${patientID}`);
          console.log(`   📧 Patient email: ${responseData?.data?.email || 'N/A'}`);
          console.log(`   👤 Patient status: ${responseData?.data?.status || 'N/A'}`);
          console.log(`   🕐 Joined at: ${responseData?.data?.joinedCallAt || 'N/A'}`);
        } else {
          console.log(`   ⚠️  No patient found (expected if no active slot or no available patients)`);
          console.log(`   📋 Response message: ${responseData?.message || 'N/A'}`);
        }

        results.push({
          doctor: doctor.username,
          hasActiveSlot: true,
          patientFound,
          patientID,
          success: true
        });
      } else {
        console.log(`   ❌ API Error: ${result.error}`);
        console.log(`   📋 Error data: ${result.data ? JSON.stringify(result.data, null, 2) : 'No error data'}`);

        results.push({
          doctor: doctor.username,
          hasActiveSlot: false,
          patientFound: false,
          success: false,
          error: result.error
        });
      }

    } catch (error) {
      console.log(`   ❌ Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      results.push({
        doctor: doctor.username,
        hasActiveSlot: false,
        patientFound: false,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
  
  // Summary
  console.log(`\n📊 getNextPatientAutomatically RANGE-AWARE TEST SUMMARY`);
  console.log('=' .repeat(80));
  
  const successfulTests = results.filter(r => r.success).length;
  const patientsFound = results.filter(r => r.patientFound).length;
  
  console.log(`   🎯 Successful tests: ${successfulTests}/${TEST_DOCTORS.length}`);
  console.log(`   👥 Patients found: ${patientsFound}/${TEST_DOCTORS.length}`);
  console.log(`   📊 Per-doctor breakdown:`);
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    const patientStatus = result.patientFound ? `Patient: ${result.patientID}` : 'No patient';
    console.log(`      ${status} ${result.doctor}: ${patientStatus}`);
    if (!result.success && result.error) {
      console.log(`         Error: ${result.error}`);
    }
  });
}

async function testNoActiveSlotScenario(): Promise<void> {
  console.log('\n🚫 TESTING NO ACTIVE SLOT SCENARIO');
  console.log('=' .repeat(80));

  // Temporarily modify consultation times to be outside current active slot
  const client = await db.connect();
  try {
    await client.query('BEGIN');

    // Move all test consultations to tomorrow (no active slot today)
    const tomorrow = DateTime.now().setZone('Australia/Sydney').plus({ days: 1 });
    await client.query(`
      UPDATE consultation
      SET "consultationDate" = $1
      WHERE "patientID" LIKE 'RANGE_TEST_%'
    `, [tomorrow.toJSDate()]);

    await client.query('COMMIT');

    console.log(`   📅 Moved all test consultations to tomorrow: ${tomorrow.toFormat('yyyy-MM-dd')}`);

    // Test that getNextPatientAutomatically returns null when no active slot
    const doctor = TEST_DOCTORS[0];
    const result = await testGetNextPatientAutomaticallyAPI(doctor.accessID);

    const responseData = result.data as { data?: { patientID?: string }; message?: string };
    const hasPatient = responseData?.data?.patientID ? true : false;

    console.log(`   📊 Response when no active slot:`);
    console.log(`   📋 Patient found: ${hasPatient ? 'YES (unexpected!)' : 'NO (expected)'}`);
    console.log(`   ✅ Test result: ${!hasPatient ? 'PASSED - No patient returned when no active slot' : 'FAILED - Patient returned despite no active slot'}`);
    console.log(`   📊 API success: ${result.success}`);
    console.log(`   📊 Status code: ${result.statusCode}`);

    // Restore consultation times for other tests
    await client.query('BEGIN');
    const now = DateTime.now().setZone('Australia/Sydney');
    const currentMinute = now.minute;
    const currentSlotStart = currentMinute < 30 ?
      now.set({ minute: 0, second: 0, millisecond: 0 }) :
      now.set({ minute: 30, second: 0, millisecond: 0 });

    await client.query(`
      UPDATE consultation
      SET "consultationDate" = $1
      WHERE "patientID" LIKE 'RANGE_TEST_%'
    `, [currentSlotStart.toJSDate()]);

    await client.query('COMMIT');
    console.log(`   🔄 Restored consultation times to current active slot`);

  } catch (error) {
    await client.query('ROLLBACK');
    console.error(`   ❌ Error in no active slot test: ${error}`);
  } finally {
    client.release();
  }
}

async function cleanupTestData(): Promise<void> {
  console.log('\n🧹 CLEANING UP RANGE-AWARE TEST DATA');

  const client = await db.connect();
  try {
    await client.query('BEGIN');

    await client.query(`DELETE FROM patientqueue WHERE "patientID" LIKE 'RANGE_TEST_%'`);
    await client.query(`DELETE FROM consultation WHERE "patientID" LIKE 'RANGE_TEST_%'`);
    await client.query(`DELETE FROM patient WHERE "patientID" LIKE 'RANGE_TEST_%'`);
    await client.query(`DELETE FROM patientslot WHERE patient_id LIKE 'RANGE_TEST_%'`);
    await client.query(`DELETE FROM slot WHERE range_id LIKE 'range-test-%'`);
    await client.query(`DELETE FROM range WHERE id LIKE 'range-test-%'`);
    await client.query(`DELETE FROM dr WHERE "accessID" LIKE 'range-test-dr-%'`);

    await client.query('COMMIT');
    console.log('   ✅ Test data cleaned up successfully');

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('   ❌ Error cleaning up test data:', error);
  } finally {
    client.release();
  }
}

async function main(): Promise<void> {
  console.log('🧪 getNextPatientAutomatically RANGE-AWARE TEST');
  console.log('📅 Test run:', new Date().toISOString());
  console.log('🕐 Current Sydney time:', DateTime.now().setZone('Australia/Sydney').toFormat('h:mm:ss a, dd MMM yyyy'));
  console.log('🎯 Testing: Updated getNextPatientAutomatically with range-aware and active slot logic');
  console.log('📋 Scenario: 3 doctors, 5 patients each, testing range filtering and active slot detection');

  try {
    await createRangeAwareTestData();
    await testActiveSlotDetection();
    await testGetNextPatientAutomaticallyRangeAware();
    await testNoActiveSlotScenario();

    console.log('\n🎉 getNextPatientAutomatically RANGE-AWARE TEST COMPLETED!');
    console.log('\n📊 KEY FINDINGS:');
    console.log('   ✅ Method now integrates with Dynamic Time Slot system');
    console.log('   ✅ Only selects patients in currently active consultation slots');
    console.log('   ✅ Respects doctor-patient assignments via patientslot/range tables');
    console.log('   ✅ Returns empty when no active slot is detected');
    console.log('   ✅ Maintains existing API contract and response structure');

  } catch (error) {
    console.error('\n❌ TEST FAILED:', error);
  } finally {
    await cleanupTestData();
    process.exit(0);
  }
}

// Run the test
main().catch(console.error);
