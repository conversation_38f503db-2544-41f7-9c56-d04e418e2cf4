import { db } from '../utils/db';
import { getPatientsInCurrentActiveSlot } from '../utils/timeSlotUtils';
import { DateTime } from 'luxon';

// Handle EPIPE errors gracefully
process.stdout.on('error', (err: NodeJS.ErrnoException) => {
  if (err.code === 'EPIPE') {
    process.exit(0);
  }
});

interface TestDoctor {
  accessID: string;
  username: string;
  id: string;
}

const TEST_DOCTORS: TestDoctor[] = [
  { accessID: 'sql-test-dr-1', username: 'TEST_Dr_SQL_1', id: '' },
  { accessID: 'sql-test-dr-2', username: 'TEST_Dr_SQL_2', id: '' }
];

async function createSQLTestData(): Promise<void> {
  console.log('\n🔧 CREATING SQL TEST DATA FOR getNextPatientAutomatically');
  
  const client = await db.connect();
  try {
    await client.query('BEGIN');

    // Clean up any existing test data
    await client.query(`DELETE FROM patientqueue WHERE "patientID" LIKE 'SQL_TEST_%'`);
    await client.query(`DELETE FROM consultation WHERE "patientID" LIKE 'SQL_TEST_%'`);
    await client.query(`DELETE FROM patient WHERE "patientID" LIKE 'SQL_TEST_%'`);
    await client.query(`DELETE FROM patientslot WHERE patient_id LIKE 'SQL_TEST_%'`);
    await client.query(`DELETE FROM slot WHERE range_id LIKE 'sql-test-%'`);
    await client.query(`DELETE FROM range WHERE id LIKE 'sql-test-%'`);
    await client.query(`DELETE FROM dr WHERE "accessID" LIKE 'sql-test-dr-%'`);

    // Create test doctors and get their IDs
    for (let i = 0; i < TEST_DOCTORS.length; i++) {
      const doctor = TEST_DOCTORS[i];
      const result = await client.query(`
        INSERT INTO dr ("accessID", username, email, "createdAt", "updatedAt")
        VALUES ($1, $2, $3, NOW(), NOW())
        ON CONFLICT ("accessID") DO UPDATE SET
          username = EXCLUDED.username,
          "updatedAt" = NOW()
        RETURNING id
      `, [doctor.accessID, doctor.username, `${doctor.username}@test.com`]);
      
      TEST_DOCTORS[i].id = result.rows[0].id;
      console.log(`   ✅ Created doctor: ${doctor.username} (ID: ${doctor.id})`);
    }

    // Create realistic time ranges for today - current active slot
    const now = DateTime.now().setZone('Australia/Sydney');
    const currentMinute = now.minute;
    let currentSlotStart: DateTime;

    if (currentMinute < 30) {
      currentSlotStart = now.set({ minute: 0, second: 0, millisecond: 0 });
    } else {
      currentSlotStart = now.set({ minute: 30, second: 0, millisecond: 0 });
    }

    const slotStart = currentSlotStart;
    const slotEnd = slotStart.plus({ minutes: 30 });

    // Create ranges and patients for each doctor
    for (let i = 0; i < TEST_DOCTORS.length; i++) {
      const doctor = TEST_DOCTORS[i];
      const rangeId = `sql-test-${i + 1}`;
      
      // Create range for this doctor
      await client.query(`
        INSERT INTO range (id, day, date, start, "end", interval, availability, status, "doctorID", "createdAt", "updatedAt")
        VALUES ($1, $2, $3, $4, $5, $6, $7, 'active', $8, NOW(), NOW())
      `, [
        rangeId,
        now.toFormat('cccc').toLowerCase(),
        now.toFormat('yyyy-MM-dd'),
        slotStart.toFormat('HH:mm'),
        slotEnd.toFormat('HH:mm'),
        30,
        20,
        doctor.id
      ]);
      
      // Create a slot for this range
      const slotResult = await client.query(`
        INSERT INTO slot (range_id, slot, remaining, "createdAt", "updatedAt", "noShowRemaining")
        VALUES ($1, $2, $3, NOW(), NOW(), $4)
        RETURNING id
      `, [
        rangeId,
        `${slotStart.toFormat('HH:mm')} - ${slotEnd.toFormat('HH:mm')}`,
        20,
        20
      ]);

      const slotId = slotResult.rows[0].id;

      // Create patients for this doctor
      const patientsPerDoctor = 3;
      for (let j = 0; j < patientsPerDoctor; j++) {
        const patientCounter = i * patientsPerDoctor + j + 1;
        const patientID = `SQL_TEST_${patientCounter.toString().padStart(3, '0')}`;
        const zohoID = `SQL_TEST_ZOHO_${patientCounter.toString().padStart(3, '0')}`;
        const patientEmail = `${patientID}@test.com`;

        // Create patient
        await client.query(`
          INSERT INTO patient ("patientID", "fullName", "email", "mobile", "zohoID", "returningPatient", "riskRating", "createdAt", "updatedAt")
          VALUES ($1, $2, $3, '**********', $4, $5, $6, NOW(), NOW())
          ON CONFLICT ("patientID") DO UPDATE SET
            "fullName" = EXCLUDED."fullName",
            "email" = EXCLUDED."email",
            "zohoID" = EXCLUDED."zohoID",
            "updatedAt" = NOW()
          RETURNING "patientID", "email"
        `, [
          patientID,
          `SQL Test Patient ${patientCounter}`,
          patientEmail,
          zohoID,
          Math.random() > 0.7,
          Math.floor(Math.random() * 5) + 1
        ]);

        console.log(`      ✅ Created patient: ${patientID} with email: ${patientEmail}`);

        // Verify the patient exists in the database
        const verifyPatient = await client.query(`SELECT email FROM patient WHERE "patientID" = $1`, [patientID]);
        if (verifyPatient.rows.length === 0) {
          throw new Error(`Patient ${patientID} was not found after insertion`);
        }
        console.log(`      ✅ Verified patient exists with email: ${verifyPatient.rows[0].email}`);

        // Assign patient to doctor's range via patientslot
        await client.query(`
          INSERT INTO patientslot (patient_id, range_id, slot_id, "createdAt", "updatedAt")
          VALUES ($1, $2, $3, NOW(), NOW())
        `, [zohoID, rangeId, slotId]);

        // Create consultation for this patient at the shared slot time
        await client.query(`
          INSERT INTO consultation ("patientID", "consultationDate", "notificationSent", completed, "createdAt", "updatedAt")
          VALUES ($1, $2, false, false, NOW(), NOW())
          ON CONFLICT ("patientID", "consultationDate") DO UPDATE SET
            "notificationSent" = false,
            completed = false,
            "updatedAt" = NOW()
        `, [patientID, slotStart.toJSDate()]);

        // Create patient queue entries (all online) - use the exact same email as in patient table
        console.log(`      📋 Creating patientqueue entry for: ${patientID} with email: ${verifyPatient.rows[0].email}`);
        await client.query(`
          INSERT INTO patientqueue ("patientID", "email", status, "notificationSentDateTime", "createdAt", "updatedAt")
          VALUES ($1, $2, 'ONLINE', NOW(), NOW(), NOW())
          ON CONFLICT ("patientID") DO UPDATE SET
            status = 'ONLINE',
            "notificationSentDateTime" = NOW(),
            "updatedAt" = NOW()
        `, [patientID, verifyPatient.rows[0].email]);
        console.log(`      ✅ Created patientqueue entry for: ${patientID}`);
      }

      console.log(`   📅 Created range for ${doctor.username}: ${slotStart.toFormat('h:mm a')} - ${slotEnd.toFormat('h:mm a')} with ${patientsPerDoctor} patients`);
    }

    await client.query('COMMIT');
    console.log(`\n   ✅ Created ${TEST_DOCTORS.length} test doctors with ranges and patients`);

  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

async function testRangeAwareSQL(): Promise<void> {
  console.log('\n🧪 TESTING RANGE-AWARE SQL QUERIES');
  console.log('=' .repeat(80));

  // First, get the active slot data
  const activeSlotData = await getPatientsInCurrentActiveSlot(db);
  const { patientIDs: activePatientIDs, activeSlotInfo } = activeSlotData;

  console.log(`\n📊 ACTIVE SLOT DETECTION:`);
  console.log(`   📅 Active slot time: ${activeSlotInfo?.slotTime.toFormat('h:mm a') || 'None'}`);
  console.log(`   👥 Patients in active slot: ${activePatientIDs.length}`);
  console.log(`   📋 First 5 patient IDs: [${activePatientIDs.slice(0, 5).join(', ')}]`);

  if (activePatientIDs.length === 0) {
    console.log(`   ⚠️  No active slot detected - testing will show empty results`);
  }

  const client = await db.connect();
  try {
    for (const doctor of TEST_DOCTORS) {
      console.log(`\n🩺 Testing SQL queries for: ${doctor.username} (${doctor.accessID})`);
      console.log('-'.repeat(60));

      // Test the range-aware query (same as in getNextPatientAutomatically)
      const rangeAwareQuery = `
        WITH RankedAdmissions AS (
          SELECT
              "email",
              "patientID",
              "drId",
              ROW_NUMBER() OVER (PARTITION BY "drId" ORDER BY "createdAt" DESC) AS row_rank
          FROM
              Admission
          ),
          LatestConsultation AS (
            SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.id, c."patientID", c."consultationDate", c."notificationSent", c.completed
            FROM Consultation c
            WHERE c."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
            AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
            ORDER BY c."createdAt", c."consultationDate", c."patientID" DESC
          )
        SELECT pq.*, p."fullName", ps.range_id, r.start as range_start, r."end" as range_end
        FROM patientqueue pq
        JOIN patient p ON pq."patientID" = p."patientID"
        JOIN patientslot ps ON p."zohoID" = ps.patient_id
        JOIN range r ON ps.range_id = r.id
        JOIN dr d ON r."doctorID" = d.id
        LEFT JOIN RankedAdmissions ra ON pq."patientID" = ra."patientID" AND ra.row_rank = 1
        LEFT JOIN LatestConsultation lc ON p."patientID" = lc."patientID"
        WHERE
            pq."notificationSentDateTime" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
            AND pq."notificationSentDateTime" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
            AND pq."completedAt" IS NULL
            AND (pq.status = 'ONLINE' OR pq.status = 'JOINED')
            AND pq."noShow" IS NULL
            AND d."accessID" = $1
            AND (ra."patientID" IS NULL OR ra."drId" = $1)
            AND (lc.completed = false OR lc.completed IS NULL)
            AND p."patientID" = ANY($2::text[])
        ORDER BY pq."joinedCallAt" ASC, p."returningPatient" DESC, p."riskRating" ASC, pq."updatedAt" ASC;
      `;

      const result = await client.query(rangeAwareQuery, [doctor.accessID, activePatientIDs]);
      
      console.log(`   📊 Query results: ${result.rows.length} patients found`);
      
      if (result.rows.length > 0) {
        console.log(`   ✅ Range-aware filtering working!`);
        result.rows.forEach((row, index) => {
          console.log(`      ${index + 1}. Patient: ${row.patientID} (${row.fullName})`);
          console.log(`         📧 Email: ${row.email}`);
          console.log(`         📅 Range: ${row.range_start} - ${row.range_end}`);
          console.log(`         🔗 Range ID: ${row.range_id}`);
          console.log(`         📊 Status: ${row.status}`);
        });
      } else {
        console.log(`   ⚠️  No patients found for this doctor`);
        console.log(`   🔍 Possible reasons:`);
        console.log(`      - No patients in active slot for this doctor`);
        console.log(`      - All patients already completed/admitted`);
        console.log(`      - No patients with ONLINE/JOINED status`);
      }
    }

  } catch (error) {
    console.error(`   ❌ SQL Error: ${error}`);
  } finally {
    client.release();
  }
}

async function cleanupSQLTestData(): Promise<void> {
  console.log('\n🧹 CLEANING UP SQL TEST DATA');

  const client = await db.connect();
  try {
    await client.query('BEGIN');

    await client.query(`DELETE FROM patientqueue WHERE "patientID" LIKE 'SQL_TEST_%'`);
    await client.query(`DELETE FROM consultation WHERE "patientID" LIKE 'SQL_TEST_%'`);
    await client.query(`DELETE FROM patient WHERE "patientID" LIKE 'SQL_TEST_%'`);
    await client.query(`DELETE FROM patientslot WHERE patient_id LIKE 'SQL_TEST_%'`);
    await client.query(`DELETE FROM slot WHERE range_id LIKE 'sql-test-%'`);
    await client.query(`DELETE FROM range WHERE id LIKE 'sql-test-%'`);
    await client.query(`DELETE FROM dr WHERE "accessID" LIKE 'sql-test-dr-%'`);

    await client.query('COMMIT');
    console.log('   ✅ SQL test data cleaned up successfully');

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('   ❌ Error cleaning up SQL test data:', error);
  } finally {
    client.release();
  }
}

async function main(): Promise<void> {
  console.log('🧪 getNextPatientAutomatically SQL RANGE-AWARE TEST');
  console.log('📅 Test run:', new Date().toISOString());
  console.log('🕐 Current Sydney time:', DateTime.now().setZone('Australia/Sydney').toFormat('h:mm:ss a, dd MMM yyyy'));
  console.log('🎯 Testing: Range-aware SQL queries for getNextPatientAutomatically');

  try {
    await createSQLTestData();
    await testRangeAwareSQL();

    console.log('\n🎉 SQL RANGE-AWARE TEST COMPLETED!');
    console.log('\n📊 KEY FINDINGS:');
    console.log('   ✅ SQL queries now include range filtering via patientslot/range joins');
    console.log('   ✅ Only patients in active consultation slots are selected');
    console.log('   ✅ Doctor-patient assignments are respected via range.doctorID');
    console.log('   ✅ Active slot filtering prevents out-of-range patient selection');

  } catch (error) {
    console.error('\n❌ SQL TEST FAILED:', error);
  } finally {
    await cleanupSQLTestData();
    process.exit(0);
  }
}

// Run the test
main().catch(console.error);
