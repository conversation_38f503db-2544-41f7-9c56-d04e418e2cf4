# Dynamic Time Slot Notifications System

## Overview

The Dynamic Time Slot Notifications System intelligently determines when to notify patients based on real-time analysis of consultation patterns. Instead of using hardcoded time intervals, the system automatically detects consultation intervals from actual data and only notifies patients whose consultation times fall within the currently active time range.

**🎯 Key Change**: The system now uses a **simplified individual doctor queue approach** where each doctor processes their own pre-assigned patients independently, eliminating complex distribution calculations while maintaining fair and efficient patient notifications.

## Features

### 🎯 Core Functionality

- **Dynamic interval detection**: Automatically analyzes consultation patterns to determine time intervals
- **Active slot identification**: Identifies the currently active consultation time range
- **Real-time filtering**: Only notifies patients in the active time slot
- **Adaptive scheduling**: Automatically adjusts to changes in consultation patterns
- **Individual doctor queues**: Each doctor processes their own pre-assigned patients independently
- **Simplified logic**: No complex distribution calculations - each doctor gets their own patients
- **Time-based activation**: Notifications only sent when consultation slots are active

### 🔄 Smart Detection

- **Pattern analysis**: Examines gaps between consecutive consultations
- **Frequency-based selection**: Chooses the most common interval as the standard
- **Timezone-aware**: <PERSON><PERSON>ly handles Australia/Sydney timezone conversions
- **Data-driven decisions**: Uses actual consultation data, not assumptions
- **Time pressure awareness**: Applies urgency boost when slots are ending soon

### 📊 Benefits

- **Prevents early notifications**: Patients only notified when their slot is active
- **Reduces no-shows**: Timely notifications increase attendance rates
- **Flexible scheduling**: Adapts to different consultation interval patterns
- **Improved patient experience**: Notifications sent at optimal times
- **Simplified maintenance**: No complex distribution logic to debug or maintain
- **Natural load balancing**: Each doctor processes their own assigned patients
- **Zelda compatible**: Handles odd time ranges (e.g., 3:42 PM - 4:12 PM) seamlessly
- **Time-window based**: No notifications sent outside active consultation windows

## ⚠️ Important Behavioral Change

### When Notifications Are Sent vs. Not Sent

| Time Scenario                    | Active Slot Detected? | Notifications Sent? | Example                                   |
| -------------------------------- | --------------------- | ------------------- | ----------------------------------------- |
| **Within consultation window**   | ✅ **YES**            | ✅ **YES**          | 2:35 PM (in 2:30-3:00 slot)               |
| **Between consultation windows** | ❌ **NO**             | ❌ **NO**           | 3:05 PM (between 3:00-3:30 and 3:30-4:00) |
| **Before first consultation**    | ❌ **NO**             | ❌ **NO**           | 1:00 PM (first consult at 2:30 PM)        |
| **After last consultation**      | ❌ **NO**             | ❌ **NO**           | 6:00 PM (last consult ended at 5:30 PM)   |

### Example Timeline

```
📅 Doctor Schedule: 2:30 PM - 5:30 PM (3 slots)
   Slot 1: 2:30 PM - 3:00 PM  ✅ Notifications sent
   Slot 2: 3:30 PM - 4:00 PM  ✅ Notifications sent
   Slot 3: 5:00 PM - 5:30 PM  ✅ Notifications sent

❌ Gap Times (NO notifications):
   - 1:00 PM - 2:30 PM (before first slot)
   - 3:00 PM - 3:30 PM (between slots)
   - 4:00 PM - 5:00 PM (between slots)
   - 5:30 PM onwards (after last slot)
```

**This prevents patient frustration and ensures notifications are only sent when doctors are actually available.**

## How It Works

### 1. Dynamic Interval Detection Process

```
Today's Consultations → Analyze Gaps → Detect Pattern → Identify Active Slot → Individual Processing
     [11:30, 12:00,      [30min,        30min is        Current time in      Each doctor processes
      12:30, 13:00]       30min,         most common     12:00-12:30 slot?    their own patients
                          30min]
```

### 2. Simplified Individual Doctor Processing

```
Current Time: 12:15 PM
Detected Interval: 30 minutes
Consultations: [11:30, 12:00, 12:30, 13:00]
Active Slot: 12:00 PM - 12:30 PM ✅

Individual Doctor Processing:
- Doctor A: Processes their own pre-assigned patients in active slot
- Doctor B: Processes their own pre-assigned patients in active slot
- Doctor C: Processes their own pre-assigned patients in active slot
- Dynamic Limit: Each doctor gets 3 patients max (calculated individually)
```

### 3. Simplified Real-World Example

**Scenario**: 3 doctors online, consultations at 9:00 AM

- **9:05 AM**: System detects 30-minute intervals, active slot is 9:00-9:30 AM
- **Individual Processing**: Each doctor processes their own pre-assigned patients
- **Dr. Smith**: Has 5 patients assigned to 9:00 AM slot → notifies 3 (dynamic limit)
- **Dr. Johnson**: Has 4 patients assigned to 9:00 AM slot → notifies 3 (dynamic limit)
- **Dr. Brown**: Has 3 patients assigned to 9:00 AM slot → notifies 3 (all patients)
- **9:35 AM**: Active slot becomes 9:30-10:00 AM, each doctor processes their 9:30 patients
- **Between slots (9:30-9:35 AM)**: No notifications sent (no active slot)

## Technical Implementation

### Core Functions

#### `fetchTodaysConsultations(db)`

Retrieves all consultations for the current day with proper timezone filtering.

```typescript
// Fetches consultations using Sydney timezone for date filtering
// Returns raw UTC timestamps for JavaScript processing
```

#### `detectConsultationInterval(consultations)`

Analyzes consultation patterns to determine the most common interval.

```typescript
// Calculates time differences between consecutive consultations
// Returns the most frequently occurring interval in minutes
```

#### `findCurrentActiveSlot(consultations, interval)`

Identifies which consultation slot is currently active.

```typescript
// Checks if current Sydney time falls within any consultation slot
// Returns slot information or null if no active slot
```

#### `getPatientsInCurrentActiveSlot(db)`

Main function that combines all logic to find patients needing notification.

```typescript
// Returns: {
//   patientIDs: string[],
//   detectedInterval: number,
//   totalConsultations: number,
//   activeSlotInfo: SlotInfo | null
// }
```

#### `calculateDynamicPatientLimit(doctorID)`

Calculates the dynamic patient limit for a doctor based on current active slot conditions.

```typescript
// Returns dynamic limit (typically 3 patients) based on:
// - Time remaining in slot
// - Number of patients in active slot
// - Current doctor patient count
```

#### `getNextPatientsForDoctor(db, doctorName)`

Gets the next patients for a specific doctor from their pre-assigned queue.

```typescript
// Returns patients assigned to this doctor in the current active slot
// Filters by PatientSlot assignments, not distribution calculations
```

### Integration Points

#### Timer System Integration

```typescript
// In startTimer() function - runs every 60 seconds
const notifiedPatients = await notifyNextPatientMiddleWare(doctorID);
```

#### Simplified Notification Middleware

```typescript
// Simplified notifyNextPatientMiddleWare with individual doctor processing
export const notifyNextPatientMiddleWare = async (doctorName: string) => {
  // Get patients for this specific doctor from their pre-assigned queue
  const nextPatients = await getNextPatientsForDoctor(db, doctorName);

  // If no patients in active slot, return empty (no notifications sent)
  if (nextPatients.length === 0) {
    return [];
  }

  // Process notifications for this doctor's patients
  const notifiedPatients = [];
  for (const patient of nextPatients) {
    // Send notification via Zoho API
    await sendPatientNotification(patient);
    notifiedPatients.push(patient);
  }

  return notifiedPatients;
};
```

#### Doctor Queue Manager

```typescript
// Uses dynamic slot detection and individual doctor processing
export const getNextPatientsForDoctor = async (db: Pool, doctorName: string) => {
  // Get active slot data first
  const activeSlotData = await getPatientsInCurrentActiveSlot(db);

  // If no active slot, return empty (no notifications)
  if (activeSlotData.patientIDs.length === 0) {
    return [];
  }

  // Get patients assigned to this specific doctor in the active slot
  const doctorPatients = await getDoctorPatientsInActiveSlot(db, doctorName, activeSlotData.patientIDs);

  return doctorPatients;
};
```

## Configuration

### Database Requirements

- **Consultation table**: Must have `consultationDate` in UTC
- **Patient table**: Must have `patientID` for linking
- **PatientQueue table**: Used for notification tracking

### Timezone Handling

```typescript
// All times stored in UTC, converted to Sydney for processing
const sydneyTime = DateTime.fromJSDate(new Date(utcTimestamp)).setZone('Australia/Sydney');
```

## Testing

### Test Scripts

#### Basic Dynamic Slot Testing

```bash
npx tsx src/utils/testDynamicSlots.ts
```

#### Simplified Notification Testing

```bash
npx tsx src/utils/testSimplifiedNotifications.ts
```

#### Zelda Compatibility Testing

```bash
# Tests odd time ranges like 3:42 PM - 4:12 PM
npx tsx src/utils/testSimplifiedNotifications.ts
```

#### Debug Interval Detection

```bash
npx tsx src/utils/debugDynamicSlots.ts
```

#### Verification Script

```bash
npx tsx src/utils/verifyCorrectLogic.ts
```

### Test Data Creation

```bash
# Create realistic test data that mirrors actual system flow
psql postgresql://docui:docui@localhost:5433/docui -f create-realistic-test-data.sql
```

### Expected Output

```
🧪 SIMPLIFIED NOTIFICATION SYSTEM TEST
📅 Test run: 2025-06-30T05:02:01.347Z
🕐 Current Sydney time: 3:02:01 PM, 30 Jun 2025
📋 Scenario: 3 doctors, 15 patients each, ALL in the CURRENT ACTIVE time slot

✅ Active slot: 3:00 PM - 3:30 PM
📊 Patients in active slot: 46
⏱️ Detected interval: 30 minutes

📊 MULTI-ROUND SUMMARY:
   Round 1: TEST_Dr_Simplified_1: 3 patients ✅
           TEST_Dr_Simplified_2: 3 patients ✅
           TEST_Dr_Simplified_3: 3 patients ✅

🎯 ZELDA COMPATIBILITY: ✅ FULLY COMPATIBLE!
   ✅ Handles odd time ranges (e.g., 3:42 PM - 4:12 PM)
   ✅ Time-agnostic patient assignment via PatientSlot table
```

## Monitoring & Logging

### Key Log Messages

```
✅ Dynamic range detection for doctor [Doctor Name]
✅ Detected interval: X minutes from Y consultations
✅ Active slot: [Start Time] - [End Time]
✅ Patients in active slot: X
❌ No active slot found for current time
ℹ️  No consultations found for today
```

### Performance Metrics

- **Interval detection**: ~50-100ms for 10-20 consultations
- **Active slot identification**: ~10-20ms
- **Database queries**: 1 query per execution
- **Memory usage**: Minimal (processes data in-memory)

## Algorithm Details

### Interval Detection Algorithm

```typescript
1. Fetch all consultations for today (Sydney timezone date range)
2. Sort consultations by time
3. Calculate time differences between consecutive consultations
4. Count frequency of each interval
5. Return the most common interval (mode)
6. Default to 30 minutes if no clear pattern
```

### Active Slot Logic

```typescript
1. Get current Sydney time
2. For each consultation:
   a. Calculate slot start (consultation time)
   b. Calculate slot end (consultation time + interval)
   c. Check if current time falls within slot
3. Return first matching slot or null
```

## Edge Cases Handled

### No Consultations Today

```typescript
// Returns empty result, no notifications sent
return { patientIDs: [], detectedInterval: 30, totalConsultations: 0 };
```

### Single Consultation

```typescript
// Uses default 30-minute interval
const defaultInterval = 30;
```

### Irregular Patterns

```typescript
// Chooses most frequent interval, handles mixed patterns
// Example: [30, 30, 60, 30] → selects 30 minutes (most common)
```

### Timezone Edge Cases

```typescript
// Handles daylight saving transitions
// Uses Australia/Sydney timezone consistently
```

## Troubleshooting

### Common Issues

#### No Active Slot Found

**Symptoms**: Log shows "No active slot found for current time"
**Causes**:

- Current time is outside consultation hours
- All consultations for today have ended
- No consultations scheduled for today

**Solutions**:

1. Check consultation schedule for today
2. Verify current Sydney time vs consultation times
3. Review consultation data in database

#### Incorrect Interval Detection

**Symptoms**: Wrong interval detected (e.g., 60 min instead of 30 min)
**Causes**:

- Irregular consultation patterns
- Missing consultations in data
- Timezone conversion issues

**Solutions**:

1. Review consultation data for patterns
2. Check timezone handling in queries
3. Verify consultation scheduling consistency

#### Patients Not Being Notified

**Symptoms**: Active slot found but no patients notified
**Causes**:

- Patients already notified
- Doctor notification limits reached
- Patients not in PatientQueue

**Solutions**:

1. Check PatientQueue status
2. Verify doctor notification capacity
3. Review patient eligibility criteria

### Debug Commands

```bash
# Check current active slot
npx tsx src/utils/testDynamicSlots.ts

# Debug interval detection logic
npx tsx src/utils/debugDynamicSlots.ts

# Verify timezone handling
npx tsx src/utils/verifyCorrectLogic.ts

# Check consultation data
psql -c "SELECT * FROM Consultation WHERE DATE(\"consultationDate\" AT TIME ZONE 'Australia/Sydney') = CURRENT_DATE"
```

## Performance Optimization

### Database Optimization

- **Indexed queries**: Uses existing indexes on `consultationDate`
- **Date filtering**: Efficient Sydney timezone date range filtering
- **Single query**: Fetches all needed data in one database call

### Memory Efficiency

- **Streaming processing**: Processes consultations without large memory allocation
- **Minimal data structures**: Only stores essential consultation information
- **Garbage collection friendly**: No long-lived object references

### Execution Time

- **Typical performance**: 100-200ms for 20 consultations
- **Scalability**: Linear time complexity O(n) where n = consultations
- **Caching potential**: Results could be cached for 1-minute intervals

## Integration with Other Systems

### Escalating Notifications

The dynamic time slot system works seamlessly with escalating notifications:

```typescript
// Escalating notifications only process patients in active slots
const activeSlotData = await getPatientsInCurrentActiveSlot(db);
const candidatesInActiveSlots = candidates.filter((c) => activeSlotData.patientIDs.includes(c.patientID));
```

### Doctor Queue Management

```typescript
// Simplified queue manager - each doctor processes their own patients
const nextPatients = await getNextPatientsForDoctor(db, doctorName);
// Returns patients assigned to this doctor in the current active slot
// Returns empty array if no active slot (no notifications sent)
```

## Key Implementation Changes

### Before (Complex Distribution)

- Calculated fair distribution among all online doctors
- Complex remainder handling and distribution logic
- Potential race conditions between doctors
- Difficult to debug and maintain

### After (Simplified Individual Processing)

- Each doctor processes their own pre-assigned patients
- No complex distribution calculations needed
- No race conditions between doctors
- Much easier to debug and maintain
- Natural load balancing through patient assignments

### Migration Benefits

- ✅ **Simplified codebase**: Removed 200+ lines of complex distribution logic
- ✅ **Better reliability**: No race conditions between doctors
- ✅ **Easier debugging**: Each doctor's notifications are independent
- ✅ **Zelda compatible**: Handles any time format seamlessly
- ✅ **Time-window based**: Only sends notifications during active consultation slots
- ✅ **Maintained fairness**: PatientSlot assignments ensure fair distribution

---

_Last updated: June 2025 - Simplified Implementation_
