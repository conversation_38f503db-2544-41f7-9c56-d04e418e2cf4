# Escalating Patient Notifications System

## Overview

The Escalating Notifications System automatically sends follow-up notifications to patients who have been notified for their consultation but haven't come online within their active consultation time ranges. This system helps ensure patients don't miss their appointments by providing timely reminders.

## Features

### 🎯 Core Functionality

- **Time-based escalation**: Configurable 1st and 2nd warning intervals
- **Dynamic time slot filtering**: Only notifies patients in currently active consultation ranges
- **Zoho integration**: Updates Zoho fields to trigger SMS workflows
- **No database changes**: Uses existing PatientQueue and Consultation tables

### ⚙️ Configuration

- **Environment variable control**: Fully configurable via .env variables
- **Enable/disable toggle**: Can be turned on/off without code changes
- **Flexible timing**: Customizable warning intervals for different scenarios

### 🔄 Integration

- **Timer system**: Runs automatically every 60 seconds with existing doctor timer
- **Dynamic slots**: Uses real-time consultation pattern detection
- **Error handling**: Robust error management with comprehensive logging

## How It Works

### 1. Patient Flow

```
Patient Notified → 5 minutes → 1st Warning → 5 minutes → 2nd Warning
                    ↓              ↓              ↓
                 Pending      1st SMS Sent    2nd SMS Sent
```

### 2. Dynamic Time Slot Detection

- Analyzes today's consultation patterns to detect interval (e.g., 30 minutes)
- Identifies currently active time slot (e.g., 10:30 PM - 11:00 PM)
- Only processes patients whose consultations fall within the active slot

### 3. Escalation Logic

- **5+ minutes**: Send 1st warning, update `consult-follow-up-1st-warning-sms = "sent"`
- **10+ minutes**: Send 2nd warning, update `consult-follow-up-2nd-warning-sms = "sent"`
- **Active slot only**: Patients outside current consultation range are excluded

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Enable or disable escalating notifications
ENABLE_ESCALATING_NOTIFICATIONS=true

# Time in minutes after initial notification to send warnings
ESCALATION_FIRST_WARNING_MINUTES=5
ESCALATION_SECOND_WARNING_MINUTES=10
```

### Configuration Examples

#### Conservative Timing (Hospital Setting)

```bash
ESCALATION_FIRST_WARNING_MINUTES=15
ESCALATION_SECOND_WARNING_MINUTES=30
```

_Gives patients more time before escalation_

#### Aggressive Timing (Urgent Care)

```bash
ESCALATION_FIRST_WARNING_MINUTES=3
ESCALATION_SECOND_WARNING_MINUTES=6
```

_Quick escalation for time-sensitive care_

#### Maintenance Mode

```bash
ENABLE_ESCALATING_NOTIFICATIONS=false
```

_Temporarily disable all escalations_

## API Endpoints

### Test Endpoint

```
GET /api/v1/test/escalating-notifications
```

Manually triggers the escalating notifications process for testing.

**Response:**

```json
{
  "message": "Escalating notifications processed successfully"
}
```

## Database Schema

### No Schema Changes Required

The system uses existing tables:

- **PatientQueue**: `notificationSentDateTime`, `joinedAt`, `status`
- **Consultation**: `consultationDate`, `completed`
- **Patient**: `patientID`, `fullName`, `zohoID`

### Zoho Fields Updated

- `consult-follow-up-1st-warning-sms`: Set to "sent" when 1st warning is triggered
- `consult-follow-up-2nd-warning-sms`: Set to "sent" when 2nd warning is triggered

## Testing

### Test Scripts

#### Basic Testing

```bash
npx tsx src/utils/testEscalatingNotifications.ts
```

#### Configuration Testing

```bash
npx tsx src/utils/testCustomEscalationTiming.ts
```

#### Custom Configuration Testing

```bash
ESCALATION_FIRST_WARNING_MINUTES=3 ESCALATION_SECOND_WARNING_MINUTES=6 npx tsx src/utils/testEscalatingNotifications.ts
```

### Test Data Creation

```bash
psql postgresql://docui:docui@localhost:5433/docui -f create-escalation-test-data.sql
```

## Monitoring & Logging

### Log Messages

- `Found X escalation candidates in active slots`
- `1st warning sent to [Patient] (X minutes since notification, threshold: Xmin)`
- `2nd warning sent to [Patient] (X minutes since notification, threshold: Xmin)`
- `Escalating notifications are disabled via configuration`

### Performance Metrics

- Typical execution time: ~5-6 seconds for 2 patients
- Database queries: 2 queries per execution
- Zoho API calls: 1 call per escalated patient

## Architecture

### File Structure

```
apps/api/src/
├── utils/
│   ├── escalatingNotifications.ts     # Core escalation logic
│   ├── testEscalatingNotifications.ts # Basic test script
│   └── testCustomEscalationTiming.ts  # Configuration test script
├── config/
│   └── index.ts                       # Configuration management
└── controllers/doctor/
    └── index.ts                       # Timer integration
```

### Integration Points

- **Timer System**: Integrated into `startTimer()` function
- **Dynamic Slots**: Uses `getPatientsInCurrentActiveSlot()` from `timeSlotUtils`
- **Zoho API**: Uses existing `ZohoAuth` and `zohoLeadURL` patterns

## Troubleshooting

### Common Issues

#### No Escalations Sent

1. Check if feature is enabled: `ENABLE_ESCALATING_NOTIFICATIONS=true`
2. Verify patients are in active consultation slots
3. Check if enough time has elapsed (default: 5+ minutes)
4. Review logs for error messages

#### Incorrect Timing

1. Verify environment variables are set correctly
2. Restart the application after changing .env
3. Check configuration with test script

#### Zoho API Errors

1. Check Zoho authentication credentials
2. Verify patient has valid `zohoID`
3. Review Zoho API rate limits

### Debug Commands

```bash
# Check current configuration
npx tsx src/utils/testCustomEscalationTiming.ts

# Test with disabled notifications
ENABLE_ESCALATING_NOTIFICATIONS=false npx tsx src/utils/testEscalatingNotifications.ts

# Test dynamic time slot detection
npx tsx src/utils/testDynamicSlots.ts
```

## Production Deployment

### Pre-deployment Checklist

- [ ] Set appropriate `ESCALATION_FIRST_WARNING_MINUTES` for your environment
- [ ] Set appropriate `ESCALATION_SECOND_WARNING_MINUTES` for your environment
- [ ] Ensure `ENABLE_ESCALATING_NOTIFICATIONS=true` in production .env
- [ ] Test with staging data before production deployment
- [ ] Monitor logs during initial deployment

### Monitoring

- Monitor escalation frequency and patient response rates
- Track Zoho API success rates
- Review execution times and performance metrics
- Monitor patient feedback regarding notification timing

## Support

For issues or questions regarding the escalating notifications system:

1. Check the troubleshooting section above
2. Review application logs for error messages
3. Test with the provided test scripts
4. Verify configuration settings

---

_Last updated: June 2025_
