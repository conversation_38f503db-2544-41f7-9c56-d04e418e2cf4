# Escalating Notifications - Quick Reference

## 🚀 Quick Start

### Enable/Disable

```bash
# Enable (default)
ENABLE_ESCALATING_NOTIFICATIONS=true

# Disable
ENABLE_ESCALATING_NOTIFICATIONS=false
```

### Configure Timing

```bash
# Default timing
ESCALATION_FIRST_WARNING_MINUTES=5
ESCALATION_SECOND_WARNING_MINUTES=10

# Conservative (hospital)
ESCALATION_FIRST_WARNING_MINUTES=15
ESCALATION_SECOND_WARNING_MINUTES=30

# Aggressive (urgent care)
ESCALATION_FIRST_WARNING_MINUTES=3
ESCALATION_SECOND_WARNING_MINUTES=6
```

## 🧪 Testing Commands

```bash
# Basic test
npx tsx src/utils/testEscalatingNotifications.ts

# Configuration test
npx tsx src/utils/testCustomEscalationTiming.ts

# Test with custom timing
ESCALATION_FIRST_WARNING_MINUTES=3 ESCALATION_SECOND_WARNING_MINUTES=6 npx tsx src/utils/testEscalatingNotifications.ts

# Test disabled mode
ENABLE_ESCALATING_NOTIFICATIONS=false npx tsx src/utils/testEscalatingNotifications.ts

# Manual API test
curl http://localhost:5000/api/v1/test/escalating-notifications
```

## 📊 Key Metrics

- **Execution Time**: ~5-6 seconds for 2 patients
- **Frequency**: Every 60 seconds (with timer system)
- **Database Queries**: 2 per execution
- **Zoho API Calls**: 1 per escalated patient

## 🔍 Log Messages to Watch

```
✅ Found X escalation candidates in active slots
✅ 1st warning sent to [Patient] (X minutes since notification, threshold: Xmin)
✅ 2nd warning sent to [Patient] (X minutes since notification, threshold: Xmin)
ℹ️  Escalating notifications are disabled via configuration
❌ Error processing escalation for patient [Patient]: [Error]
```

## 🗂️ Files Modified

- `apps/api/src/utils/escalatingNotifications.ts` - Core logic
- `apps/api/src/config/index.ts` - Configuration
- `apps/api/src/controllers/doctor/index.ts` - Timer integration
- `apps/api/src/routes/doc.route.ts` - Test endpoint

## 🔧 Troubleshooting

| Issue               | Solution                                     |
| ------------------- | -------------------------------------------- |
| No escalations sent | Check `ENABLE_ESCALATING_NOTIFICATIONS=true` |
| Wrong timing        | Verify env vars, restart app                 |
| Zoho errors         | Check patient `zohoID`, API credentials      |
| No active slots     | Check consultation times vs current time     |

## 📋 Zoho Fields Updated

- `consult-follow-up-1st-warning-sms` → "sent"
- `consult-follow-up-2nd-warning-sms` → "sent"

## 🎯 Patient Flow

```
Notification Sent → 5min → 1st Warning → 5min → 2nd Warning
                     ↓         ↓           ↓
                  Pending   SMS Sent   SMS Sent
```

---

_Quick reference for the escalating notifications system_
