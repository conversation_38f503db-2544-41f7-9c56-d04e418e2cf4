# Dynamic Time Slot Notifications - Quick Reference

## 🎯 What It Does

**Automatically detects consultation patterns and only notifies patients during their active time slot. Each doctor processes their own pre-assigned patients independently.**

```
Consultations: [9:00, 9:30, 10:00, 10:30]
Current Time: 9:15 AM
Active Slot: 9:00-9:30 AM ✅
Each Doctor: Processes their own 9:00 AM patients
Between Slots (9:30-9:35 AM): NO notifications sent ❌
```

## 🧪 Testing Commands

```bash
# Test simplified notification system (MAIN TEST)
npx tsx src/utils/testSimplifiedNotifications.ts

# Test dynamic slot detection only
npx tsx src/utils/testDynamicSlots.ts

# Debug interval detection
npx tsx src/utils/debugDynamicSlots.ts

# Verify timezone logic
npx tsx src/utils/verifyCorrectLogic.ts
```

## 📊 Key Functions

| Function                           | Purpose                          | Returns          |
| ---------------------------------- | -------------------------------- | ---------------- |
| `getPatientsInCurrentActiveSlot()` | Main slot detection function     | `ActiveSlotData` |
| `getNextPatientsForDoctor()`       | Get doctor's patients in slot    | `Patient[]`      |
| `calculateDynamicPatientLimit()`   | Calculate doctor's patient limit | `number`         |
| `notifyNextPatientMiddleWare()`    | Send notifications (simplified)  | `Patient[]`      |

## 🔍 Expected Log Messages

```
🎯 SLOT DETECTION: Multi-cluster analysis
🎯 ACTIVE SLOT IDENTIFIED
✅ Active slot: 3:00 PM - 3:30 PM
📊 Patients in active slot: 46
⚡ RANGE DETECTION COMPLETED
📋 PATIENTS SELECTED FOR NOTIFICATION
Selected 3 patients for doctor [Name]
❌ NO ACTIVE SLOT FOUND (between consultation windows)
```

## 📈 Performance Metrics

- **Execution Time**: 100-200ms for 20 consultations
- **Database Queries**: 1 per execution
- **Memory Usage**: Minimal (in-memory processing)
- **Frequency**: Every 60 seconds with timer system

## 🔧 Algorithm Overview

### 1. Simplified Flow

```
Active Slot Detection → Individual Doctor Processing → Send Notifications
9:15 AM in 9:00-9:30? → Each doctor gets their patients → Notify via Zoho
```

### 2. Time-Window Based Behavior

```
✅ WITHIN SLOT (9:15 AM): Notifications sent
❌ BETWEEN SLOTS (9:35 AM): NO notifications sent
❌ BEFORE/AFTER HOURS: NO notifications sent
```

### 3. Individual Doctor Processing

```
Doctor A: Gets their 9:00 AM patients → Notifies up to 3
Doctor B: Gets their 9:00 AM patients → Notifies up to 3
Doctor C: Gets their 9:00 AM patients → Notifies up to 3
```

## 🗂️ Files Involved

- `apps/api/src/utils/timeSlotUtils.ts` - Core logic
- `apps/api/src/controllers/doctor/index.ts` - Integration
- `apps/api/src/controllers/doctor/doctorQueueManager.ts` - Queue management

## 🐛 Troubleshooting

| Issue                     | Cause                      | Solution                     |
| ------------------------- | -------------------------- | ---------------------------- |
| No active slot            | Outside consultation hours | Check consultation schedule  |
| No notifications          | Between time slots         | Wait for next active slot    |
| Wrong interval            | Irregular patterns         | Review consultation data     |
| Patients already notified | Reached dynamic limit      | Check doctor patient count   |
| Timezone issues           | UTC/Sydney conversion      | Verify timezone handling     |
| Zelda odd times           | Non-standard scheduling    | System handles automatically |

## 📋 Database Schema

### Required Tables

- **Consultation**: `consultationDate` (UTC), `patientID`, `completed`
- **Patient**: `patientID`, `fullName`, `zohoID`
- **PatientQueue**: `patientID`, `notificationSentDateTime`, `status`

### Key Queries

```sql
-- Fetch today's consultations (Sydney timezone filtering)
SELECT c."consultationDate", c."patientID"
FROM Consultation c
WHERE c."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
```

## 🎯 Integration Points

### Timer System

```typescript
// Runs every 60 seconds - simplified processing
await notifyNextPatientMiddleWare(doctorName);
```

### Simplified Queue Management

```typescript
// Each doctor gets their own patients from active slot
const nextPatients = await getNextPatientsForDoctor(db, doctorName);
// Returns empty array if no active slot (no notifications sent)
```

### Time-Window Behavior

```typescript
// Only sends notifications during active consultation windows
if (activeSlotData.patientIDs.length === 0) {
  return []; // No notifications sent between slots
}
```

## 🔄 Real-World Examples

### Example 1: Active slot with notifications

```
Consultations: 9:00, 9:30, 10:00, 10:30
Current Time: 9:15 AM
Active Slot: 9:00-9:30 AM ✅
Each Doctor: Processes their 9:00 AM patients
Result: Notifications sent ✅
```

### Example 2: Between slots - NO notifications

```
Consultations: 9:00, 9:30, 10:00, 10:30
Current Time: 9:35 AM (between 9:30 and 10:00)
Active Slot: None ❌
Result: NO notifications sent ❌
```

### Example 3: Zelda odd times

```
Zelda Consultation: 3:42 PM - 4:12 PM
Current Time: 3:50 PM
Active Slot: 3:42-4:12 PM ✅
Result: Zelda patients notified ✅ (system handles any time format)
```

## 📊 Sample Output

```
🧪 SIMPLIFIED NOTIFICATION SYSTEM TEST
📅 Current Sydney time: 3:02:01 PM, 30 Jun 2025
🎯 Active slot: 3:00 PM - 3:30 PM
📊 Patients in active slot: 46
⏱️ Detected interval: 30 minutes

📊 MULTI-ROUND SUMMARY:
   Round 1: TEST_Dr_Simplified_1: 3 patients ✅
           TEST_Dr_Simplified_2: 3 patients ✅
           TEST_Dr_Simplified_3: 3 patients ✅

🎯 ZELDA COMPATIBILITY: ✅ FULLY COMPATIBLE!
   📅 Simulating Zelda-style odd time range: 3:42 PM - 4:12 PM
   ✅ System handles ANY time format seamlessly
```

## ⚙️ Configuration

### No Configuration Required

The system automatically adapts to consultation patterns without configuration.

### Default Behavior

- **Default interval**: 30 minutes (if no pattern detected)
- **Timezone**: Australia/Sydney
- **Update frequency**: Every 60 seconds
- **Individual processing**: Each doctor handles their own patients
- **Time-window based**: NO notifications sent between consultation slots
- **Zelda compatible**: Handles any time format (e.g., 3:42 PM - 4:12 PM)

## 🎯 Key Behavioral Changes

### ✅ When Notifications ARE Sent

- During active consultation windows (e.g., 2:35 PM in 2:30-3:00 slot)
- Each doctor processes their own pre-assigned patients
- Dynamic limit typically 3 patients per doctor

### ❌ When Notifications ARE NOT Sent

- Between consultation windows (e.g., 3:05 PM between 3:00-3:30 and 3:30-4:00)
- Before first consultation of the day
- After last consultation of the day
- When no consultations are scheduled

---

_Quick reference for simplified dynamic time slot notifications system_
