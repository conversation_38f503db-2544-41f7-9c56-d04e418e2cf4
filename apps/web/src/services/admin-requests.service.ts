import axios from "axios";
import { config } from "../config";

const API_BASE_URL = config.apiUrl || "http://localhost:8080/api";

export interface AdminRequest {
  id: string;
  type: 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase';
  patient_id: string;
  email: string;
  patient_name: string;
  total_score: number;
  max_score: number;
  is_eligible?: boolean;
  status: 'pending' | 'submitted' | 'approved' | 'rejected';
  created_at: string;
  reviewed_at?: string;
  reviewed_by?: string;
  review_notes?: string;
  doctor_name?: string;
  questionnaire_data?: any;
  strength_requests?: Array<{
    strength: string;
    currentQuantity: number;
    requestedQuantity: number;
    increaseAmount: number;
  }>;
}

export interface AdminRequestStats {
  pending: number;
  approved: number;
  rejected: number;
  nonEligible: number;
  total: number;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedAdminRequestsResponse {
  requests: AdminRequest[];
  pagination: PaginationInfo;
}

class AdminRequestsService {
  /**
   * Get pending requests for admin view
   */
  async getPendingRequests(limit = 20, page = 1): Promise<PaginatedAdminRequestsResponse> {
    const response = await axios.get(
      `${API_BASE_URL}/admin/v1.0/requests/pending?limit=${limit}&page=${page}`,
      { withCredentials: true }
    );
    return response.data.data;
  }

  /**
   * Get processed requests for admin view
   */
  async getProcessedRequests(
    status?: 'approved' | 'rejected',
    type?: 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase',
    limit = 50,
    page = 1
  ): Promise<PaginatedAdminRequestsResponse> {
    const params = new URLSearchParams();
    if (status) params.append('status', status);
    if (type) params.append('type', type);
    params.append('limit', limit.toString());
    params.append('page', page.toString());

		const response = await axios.get(`${API_BASE_URL}/admin/v1.0/requests/processed?${params}`, {
			withCredentials: true,
		});
		return response.data.data;
	}

	/**
	 * Get request statistics for admin dashboard
	 */
	async getRequestStats(): Promise<AdminRequestStats> {
		const response = await axios.get(`${API_BASE_URL}/admin/v1.0/requests/stats`, { withCredentials: true });
		return response.data.data;
	}

  /**
   * Get non-eligible requests for admin view
   */
  async getNonEligibleRequests(limit = 20, page = 1): Promise<PaginatedAdminRequestsResponse> {
    const response = await axios.get(
      `${API_BASE_URL}/admin/v1.0/requests/non-eligible?limit=${limit}&page=${page}`,
      { withCredentials: true }
    );
    return response.data.data;
  }

  /**
   * Get patient request history for admin lookup
   */
  async getPatientRequestHistory(patientId: string, limit = 20): Promise<{ requests: AdminRequest[] }> {
    const response = await axios.get(
      `${API_BASE_URL}/admin/v1.0/requests/patient/${patientId}?limit=${limit}`,
      { withCredentials: true }
    );
    return response.data.data;
  }

  /**
   * Export admin requests to CSV
   */
  async exportRequestsToCSV(filters: {
    startDate?: string;
    endDate?: string;
    questionnaireType?: 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase';
  }): Promise<void> {
    const params = new URLSearchParams();

    if (filters.startDate) {
      params.append('startDate', filters.startDate);
    }
    if (filters.endDate) {
      params.append('endDate', filters.endDate);
    }
    if (filters.questionnaireType) {
      params.append('questionnaireType', filters.questionnaireType);
    }

    const response = await axios.get(
      `${API_BASE_URL}/admin/v1.0/requests/export/csv?${params.toString()}`,
      {
        withCredentials: true,
        responseType: 'blob'
      }
    );

    // Create blob and download file
    const blob = new Blob([response.data], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    // Generate filename with current date
    const today = new Date().toISOString().split('T')[0];
    const typeFilter = filters.questionnaireType ? `_${filters.questionnaireType}` : '';
    link.download = `admin_requests_export${typeFilter}_${today}.csv`;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }
}

export const adminRequestsService = new AdminRequestsService();
export default adminRequestsService;
