//import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Dialog<PERSON>ontent, IconButton, <PERSON>u, <PERSON>uItem, Typography, useMediaQuery, Box } from "@mui/material";

import {
	But<PERSON>,
	Collapse,
	Dialog,
	DialogContent,
	IconButton,
	Typography,
	useMediaQuery,
	Box,
	FormControl,
	InputLabel,
	Select,
	MenuItem,
	FormHelperText,
	SelectChangeEvent,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Fragment, useEffect, useState } from "react";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CircleOutlinedIcon from "@mui/icons-material/CircleOutlined";
// import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import LockIcon from "@mui/icons-material/Lock";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import NavigateBeforeIcon from "@mui/icons-material/NavigateBefore";

import { useTheme } from "@mui/material/styles";
import {
	convertDateToReadableFormat,
	// createTimeSlots
} from "../../../utils";
import { MakeGenerics, useSearch } from "@tanstack/react-location";
import { ApiClient } from "../../../services";
import { AvailableDate, Dr, Slot } from "../../../types";
import LoadingScreen from "../../../utils/loading-screen";
import { config } from "../../../config";
import PatientLanding from "./PatientLanding";
import DoctorImage from "../../shared/DoctorImage";
import {
	convertAustralianRangeToUtc,
	convertUtcToClientTime,
	getClientTimeZoneId,
} from "../../../utils/dateTimeHelpers";
import moment from "moment";

type DaysOfTheWeek = "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "";

const dayOfTheWeek: { [key: string]: DaysOfTheWeek } = {
	MONDAY: "Monday",
	TUESDAY: "Tuesday",
	WEDNESDAY: "Wednesday",
	THURSDAY: "Thursday",
	FRIDAY: "Friday",
};

type UrlProps = MakeGenerics<{
	Search: {
		token: string;
	};
}>;

type WebSocketMessage = {
	type: string;
	data: unknown;
};

const Scheduler: React.FC<{
	patientID?: string;
	isAdmin?: boolean;
	hideMobileHeader?: boolean;
	selectedDoctorId?: string;
}> = ({ patientID, isAdmin = false, hideMobileHeader, selectedDoctorId: externalSelectedDoctorId }) => {
	const { token } = useSearch<UrlProps>();
	// Check if we're on the public schedule page vs admin schedule page
	const isPublicSchedulePage = window.location.pathname === "/schedule";
	const isAdminSchedulePage = window.location.pathname.includes("/schedule-admin");

	const [selectedDay, setSelectedDay] = useState<AvailableDate | undefined>(undefined);
	const [filter, setFilter] = useState<DaysOfTheWeek>("");
	const [_anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
	const theme = useTheme();
	const isDesktopOrTablet = useMediaQuery(theme.breakpoints.up("md"));
	const [data, setData] = useState<AvailableDate[]>([]);
	const [selectedSlot, setSelectedSlot] = useState<Slot | undefined>(undefined);
	const [openDialog, setOpenDialog] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const [filteredData, setFilteredData] = useState<AvailableDate[]>([]);
	const [currentPage, setCurrentPage] = useState(0);
	const [restart, setRestart] = useState(true);
	const [showLanding, setShowLanding] = useState(!isAdmin);
	const [filterDate, setFilterDate] = useState<string | null>(null);
	const [doctors, setDoctors] = useState<Dr[]>([]);
	const [internalSelectedDoctorId, setInternalSelectedDoctorId] = useState<string | undefined>(
		externalSelectedDoctorId
	);
	const [clientTimeZone, setClientTimeZone] = useState<string>("");
	// Use either the external selectedDoctorId from props or the internal state
	// If we're on the public schedule page, only use internal selection
	const selectedDoctorId = isPublicSchedulePage
		? internalSelectedDoctorId
		: externalSelectedDoctorId || internalSelectedDoctorId;

	// Handler for doctor selection
	const handleDoctorChange = (event: SelectChangeEvent) => {
		setInternalSelectedDoctorId(event.target.value);
	};

	// Load doctors when component mounts
	useEffect(() => {
		const detectedTimeZone = getClientTimeZoneId();
		setClientTimeZone(detectedTimeZone);
		const fetchDoctors = async () => {
			try {
				const doctorsResult = await ApiClient.getActiveDoctors();
				setDoctors(doctorsResult);
			} catch (error) {
				console.error("Failed to fetch doctors:", error);
			} finally {
				// Keep isLoading true until availabilities are also loaded
			}
		};

		fetchDoctors();
	}, []);

	const itemsPerPage = 5;

	const weekDays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"];

	// Handle navigation
	const handleNext = () => {
		if ((currentPage + 1) * itemsPerPage < filteredData.length) {
			// For admins, simply go to the next page
			if (isAdmin) {
				setCurrentPage(currentPage + 1);
				return;
			}

			// For regular users, find the next page with available slots
			let nextPage = currentPage + 1;
			const maxPage = Math.ceil(filteredData.length / itemsPerPage) - 1;

			while (nextPage <= maxPage) {
				const pageRanges = filteredData.slice(nextPage * itemsPerPage, (nextPage + 1) * itemsPerPage);
				const rangesToCheck = pageRanges.slice(0, Math.min(5, pageRanges.length));

				if (rangesToCheck.some(hasAvailableSlots)) {
					break; // Found a page with available slots
				}

				nextPage++;
			}

			// If we've gone past the last page, stay on the current page
			if (nextPage > maxPage) {
				nextPage = currentPage;
			}

			setCurrentPage(nextPage);
		}
	};

	const handlePrevious = () => {
		if (currentPage > 0) {
			// For admins, simply go to the previous page
			if (isAdmin) {
				setCurrentPage(currentPage - 1);
				return;
			}

			// For regular users, find the previous page with available slots
			let prevPage = currentPage - 1;

			while (prevPage >= 0) {
				const pageRanges = filteredData.slice(prevPage * itemsPerPage, (prevPage + 1) * itemsPerPage);
				const rangesToCheck = pageRanges.slice(0, Math.min(5, pageRanges.length));

				if (rangesToCheck.some(hasAvailableSlots)) {
					break; // Found a page with available slots
				}

				prevPage--;
			}

			// If we've gone before the first page, stay on the current page
			if (prevPage < 0) {
				prevPage = 0;
			}

			setCurrentPage(prevPage);
		}
	};

	const paginatedData = filteredData.slice(currentPage * itemsPerPage, (currentPage + 1) * itemsPerPage);

	// Deduplicate paginatedData by day-of-week for the mobile day selector
	const uniqueDays: string[] = [];
	const uniquePaginatedData: AvailableDate[] = [];
	for (const value of paginatedData) {
		const dayKey = value.day ?? "";
		if (!uniqueDays.includes(dayKey)) {
			uniqueDays.push(dayKey);
			uniquePaginatedData.push(value);
		}
	}

	const handleFilterChange = (day: DaysOfTheWeek) => {
		if (filter === day) {
			setFilter("");
			setFilteredData(data);
		} else {
			setFilter(day);
			setFilteredData(data.filter((item) => item.day === day));
		}

		setSelectedSlot(undefined);
		setCurrentPage(0);
		setAnchorEl(null);
		return;
	};

	const handleDaySelected = (value: AvailableDate) => {
		// Make a copy of the day's data to avoid mutating the original data
		const dayCopy = { ...value };

		// Aggregate slots by time if in public page (patient view)
		if (isPublicSchedulePage && dayCopy.slots) {
			dayCopy.slots = aggregateSlotsByTime(dayCopy.slots);
		}

		setSelectedDay(dayCopy);
		setSelectedSlot(undefined);
	};

	// const handleMenu = (e: React.MouseEvent<HTMLElement>) => {
	//     setAnchorEl(e.currentTarget);
	// };

	// const handleClose = () => {
	//     setAnchorEl(null);
	// };

	const handleSlotSelected = (slot: Slot) => {
		setSelectedSlot(slot);
	};

	const handleSlotConfirmed = async () => {
		setIsLoading(true);
		try {
			const leadID = token ? await ApiClient.getDecryptedUrl(token) : patientID;

			if (!leadID) {
				alert("Missing Patient ID");
				setIsLoading(false);
				return;
			}

			if (!selectedSlot) {
				alert("Missing Patient Slot");
				setIsLoading(false);
				return;
			}

			// For admin bookings, include doctor ID if available
			// For patient bookings, include source_slots for random assignment
			let slotWithDoctor = selectedSlot;

			if (!isPublicSchedulePage && selectedDoctorId && selectedDay) {
				// Admin booking - include doctor ID
				slotWithDoctor = { ...selectedSlot, doctorID: selectedDay.doctorID };
			} else if (isPublicSchedulePage && selectedSlot && "source_slots" in selectedSlot) {
				// Patient booking with aggregated slot - include source_slots for random assignment
				slotWithDoctor = {
					...selectedSlot,
					source_slots: (selectedSlot as any).source_slots,
				};
			}

			// For admin bookings, pass the admin ID
			const adminId = isAdmin ? localStorage.getItem("xdr") || undefined : undefined;
			const result = await ApiClient.postPatientBooking(
				leadID,
				slotWithDoctor,
				token ? "patient" : "sales",
				adminId
			);

			if (result) {
				// Immediately refetch availabilities to ensure UI is in sync
				const doctorIdForRefresh = isPublicSchedulePage ? undefined : selectedDoctorId;
				const updatedResult = await ApiClient.getAvailabilitiesForPatient(doctorIdForRefresh);

				// For admin pages, filter by doctor ID if provided
				const filteredUpdatedResult =
					!isPublicSchedulePage && selectedDoctorId
						? updatedResult.filter((item) => item.doctorID === selectedDoctorId)
						: updatedResult;

				setData([...filteredUpdatedResult]);
				setFilteredData(filteredUpdatedResult);

				// Optionally, update selectedDay if it exists
				if (selectedDay) {
					const refreshedDay = filteredUpdatedResult.find((day) => day.range_id === selectedDay.range_id);
					if (refreshedDay) setSelectedDay(refreshedDay);
				}

				if (token) {
					const baseUrl = `${config.funnelZenithConfirmUrl}/confirm`;
					const url = `${baseUrl}?token=${token}`;
					window.location.href = url;
				} else {
					setIsLoading(false);
					setOpenDialog(true);
				}
			}
		} catch (e) {
			alert(
				"Thank you for registering! If you're unable to book your appointment, please contact our support team for assistance."
			);
			setIsLoading(false);
		}
	};

	// const handleBackButton = () => {
	//     setSelectedDay(undefined)
	// }

	// Helper function to check if a range has available slots
	const hasAvailableSlots = (range: AvailableDate): boolean => {
		const totalSlots = range.slots?.reduce((total, slot) => total + slot.remaining, 0) || 0;
		return totalSlots > 0;
	};

	// New helper function to aggregate slots by time
	const aggregateSlotsByTime = (slots: Slot[] | undefined): Slot[] => {
		if (!slots || !isPublicSchedulePage) {
			return slots || [];
		}

		// Create a map to group slots by their time string
		const slotMap = new Map<string, Slot>();

		slots.forEach((slot) => {
			const timeKey = slot.slot; // e.g. "07:30-08:00"

			if (slotMap.has(timeKey)) {
				// If we already have a slot with this time, update the remaining count
				const existingSlot = slotMap.get(timeKey)!;
				existingSlot.remaining += slot.remaining;
				if (existingSlot.noShowRemaining !== undefined && slot.noShowRemaining !== undefined) {
					existingSlot.noShowRemaining += slot.noShowRemaining;
				}

				// Combine source_slots arrays if they exist
				if (existingSlot.source_slots && slot.source_slots) {
					existingSlot.source_slots = [...existingSlot.source_slots, ...slot.source_slots];
				} else if (slot.source_slots) {
					existingSlot.source_slots = slot.source_slots;
				}
			} else {
				// First time seeing this slot time, add it to the map
				slotMap.set(timeKey, { ...slot });
			}
		});

		// Convert map back to array
		return Array.from(slotMap.values());
	};

	// Helper function to find the first page with available slots
	const findFirstPageWithAvailableSlots = (allData: AvailableDate[]): number => {
		// For admins, always start with the first page
		if (isAdmin) {
			return 0;
		}

		const rangesPerPage = itemsPerPage;

		for (let i = 0; i < Math.ceil(allData.length / rangesPerPage); i++) {
			const pageRanges = allData.slice(i * rangesPerPage, (i + 1) * rangesPerPage);

			// Check if any of the first 5 ranges (or fewer if page has less than 5) have available slots
			const rangesToCheck = pageRanges.slice(0, Math.min(5, pageRanges.length));
			if (rangesToCheck.some(hasAvailableSlots)) {
				return i;
			}
		}

		// If no page has available slots, return the first page
		return 0;
	};

	useEffect(() => {
		const fetchData = async () => {
			try {
				// On admin page, only fetch if a doctor is selected
				if (isAdminSchedulePage && !selectedDoctorId) {
					// If on admin page with no doctor selected, clear data and show empty state
					setData([]);
					setFilteredData([]);
					setSelectedDay(undefined);
					setIsLoading(false);
					return;
				}

				// Fetch real data based on context
				if (isPublicSchedulePage || selectedDoctorId || (!isPublicSchedulePage && !isAdminSchedulePage)) {
					// For public schedule page (patients), fetch aggregated data without doctorID
					// For admin pages, only fetch if doctor is selected
					const doctorIdToPass = isPublicSchedulePage ? undefined : selectedDoctorId;
					const result = await ApiClient.getAvailabilitiesForPatient(doctorIdToPass);

					// If no availabilities were found, create placeholder data
					if (result.length === 0) {
						// Create same placeholder data as above
						const today = new Date();
						const placeholderData: AvailableDate[] = [];

						const daysOfWeek = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"];
						const currentDay = today.getDay();

						for (let i = 0; i < 5; i++) {
							const nextDate = new Date(today);
							if (currentDay === 0) {
								nextDate.setDate(today.getDate() + 1 + i);
							} else if (currentDay === 6) {
								nextDate.setDate(today.getDate() + 2 + i);
							} else {
								nextDate.setDate(today.getDate() + i);
							}

							const dateStr = nextDate.toISOString().split("T")[0];
							const dayIndex = nextDate.getDay() - 1;
							const day = dayIndex >= 0 ? daysOfWeek[dayIndex] : daysOfWeek[4];

							placeholderData.push({
								range_id: `placeholder-doctor-${i}`,
								day,
								date: dateStr,
								start: "09:00",
								end: "17:00",
								interval: 30,
								availability: 0,

								status: "active",
								doctorID: selectedDoctorId || "",
								doctorName: selectedDoctorId
									? doctors.find((d) => d.id === selectedDoctorId)?.name || "Selected Doctor"
									: "",
								slots: [],
							});
						}

						setData(placeholderData);
						setFilteredData(placeholderData);
					} else {
						// Use real data
						setData([...result]);
						setFilteredData(result);
					}

					// Find the first page with available slots
					const firstAvailablePage = findFirstPageWithAvailableSlots(result.length > 0 ? result : []);

					setCurrentPage(firstAvailablePage);

					if (isDesktopOrTablet) {
						if (result.length > 0) {
							if (isAdmin) {
								// For admins, just select the first day
								setSelectedDay(result[0]);
							} else {
								// For regular users, find the first range with available slots
								const firstAvailableRange = result.find(hasAvailableSlots) || result[0];

								// Aggregate slots by time for patient view before setting selectedDay
								if (isPublicSchedulePage && firstAvailableRange.slots) {
									firstAvailableRange.slots = aggregateSlotsByTime(firstAvailableRange.slots);
								}

								setSelectedDay(firstAvailableRange);
							}
						}
					}
				} else {
					// Clear data if no doctor is selected
					setData([]);
					setFilteredData([]);
					setSelectedDay(undefined);
				}
			} catch (e) {
				const error = e as any;
				alert(error.message);
			} finally {
				setIsLoading(false);
			}
		};

		fetchData();
	}, [selectedDoctorId, isAdmin, isPublicSchedulePage, isDesktopOrTablet, isAdminSchedulePage, doctors]);

	// Update Zoho member status when the booking page is loaded
	useEffect(() => {
		const updateMemberStatus = async () => {
			try {
				// Get the leadId/zohoId from the token or patientID
				const leadID = token ? await ApiClient.getDecryptedUrl(token) : patientID;

				if (leadID) {
					// Update the member status to "8 - Booking Page Reached"
					await ApiClient.updateZohoLeadMemberStatus(leadID);
					console.log("Successfully updated member status for lead:", leadID);
				}
			} catch (error) {
				console.error("Error updating member status:", error);
				// Don't show an alert to the user as this is a background operation
			}
		};

		updateMemberStatus();
	}, [token, patientID]);

	useEffect(() => {
		const ws = new WebSocket(`${import.meta.env.VITE_WSS}`);
		ws.onmessage = (event) => {
			const message = JSON.parse(event.data) as WebSocketMessage;

			if (message.type === "new_booking") {
				const slotForSelectedDay: Slot[] = [];
				const data = message.data as Slot[];
				if (data.length > 0) {
					data.forEach((newSlot) => {
						setFilteredData((prev) =>
							prev.map((day) => {
								if (day.range_id === newSlot.range_id) {
									const updatedSlots = day.slots?.map((slot) =>
										slot.id === newSlot.id ? { ...slot, remaining: newSlot.remaining } : slot
									);

									const updatedDay = {
										...day,
										slots: updatedSlots,
									};

									return updatedDay;
								}

								return day;
							})
						);

						if (newSlot.range_id === selectedDay?.range_id) {
							slotForSelectedDay.push(newSlot);
						}
					});
				}
				if (slotForSelectedDay.length > 0 && selectedDay?.slots) {
					const combinedSlot = [
						...new Map(
							[...selectedDay.slots, ...slotForSelectedDay].map((slot) => [slot.id, slot])
						).values(),
					];
					const sortedSlots = combinedSlot.sort((a, b) => {
						const startA = a.slot.split(" - ")[0];
						const startB = b.slot.split(" - ")[0];
						const timeA = new Date(`1970-01-01T${startA}:00Z`);
						const timeB = new Date(`1970-01-01T${startB}:00Z`);

						return timeA.getTime() - timeB.getTime();
					});

					setSelectedDay(() => {
						return {
							...selectedDay,
							slots: sortedSlots,
						};
					});
				}
			}
		};

		ws.onclose = () => {
			setRestart(!restart);
		};

		ws.onerror = (error) => {
			console.log("ws error:", error);
		};
	}, [restart]);

	const desktopNextButtonColor = (() => {
		if (isAdmin) {
			const maxPage = Math.ceil(filteredData.length / itemsPerPage) - 1;
			return currentPage < maxPage ? "green" : "#CFCFCF";
		}
		const nextPage = currentPage + 1;
		const maxPage = Math.ceil(filteredData.length / itemsPerPage) - 1;
		for (let i = nextPage; i <= maxPage; i++) {
			const pageRanges = filteredData.slice(i * itemsPerPage, (i + 1) * itemsPerPage);
			const rangesToCheck = pageRanges.slice(0, Math.min(5, pageRanges.length));
			if (rangesToCheck.some(hasAvailableSlots)) return "green";
		}
		return "#CFCFCF";
	})();

	const isDesktopNextDisabled = (() => {
		if (isAdmin) {
			const maxPage = Math.ceil(filteredData.length / itemsPerPage) - 1;
			return currentPage >= maxPage;
		}
		const nextPage = currentPage + 1;
		const maxPage = Math.ceil(filteredData.length / itemsPerPage) - 1;
		for (let i = nextPage; i <= maxPage; i++) {
			const pageRanges = filteredData.slice(i * itemsPerPage, (i + 1) * itemsPerPage);
			const rangesToCheck = pageRanges.slice(0, Math.min(5, pageRanges.length));
			if (rangesToCheck.some(hasAvailableSlots)) return false;
		}
		return true;
	})();

	const mobileNextButtonColor = (() => {
		if (isAdmin) {
			const maxPage = Math.ceil(filteredData.length / itemsPerPage) - 1;
			return currentPage < maxPage ? "green" : "#CFCFCF";
		}
		const nextPage = currentPage + 1;
		const maxPage = Math.ceil(filteredData.length / itemsPerPage) - 1;
		for (let i = nextPage; i <= maxPage; i++) {
			const pageRanges = filteredData.slice(i * itemsPerPage, (i + 1) * itemsPerPage);
			const rangesToCheck = pageRanges.slice(0, Math.min(5, pageRanges.length));
			if (rangesToCheck.some(hasAvailableSlots)) return "green";
		}
		return "#CFCFCF";
	})();

	const isMobileNextDisabled = (() => {
		if (isAdmin) {
			const maxPage = Math.ceil(filteredData.length / itemsPerPage) - 1;
			return currentPage >= maxPage;
		}
		const nextPage = currentPage + 1;
		const maxPage = Math.ceil(filteredData.length / itemsPerPage) - 1;
		for (let i = nextPage; i <= maxPage; i++) {
			const pageRanges = filteredData.slice(i * itemsPerPage, (i + 1) * itemsPerPage);
			const rangesToCheck = pageRanges.slice(0, Math.min(5, pageRanges.length));
			if (rangesToCheck.some(hasAvailableSlots)) return false;
		}
		return true;
	})();

	const getMonday = (date: Date) => {
		const d = new Date(date);
		const day = d.getDay();
		const diff = d.getDate() - day + (day === 0 ? -6 : 1); // adjust when day is Sunday
		return new Date(d.setDate(diff));
	};

	let baseMonday: Date | null = null;
	if (paginatedData.length > 0) {
		// Find the earliest date in paginatedData, skipping undefined dates
		const dates = paginatedData
			.map((d) => d.date)
			.filter((d): d is string => !!d)
			.map((d) => new Date(d));
		if (dates.length > 0) {
			baseMonday = getMonday(new Date(Math.min(...dates.map((d) => d.getTime()))));
		}
	}

	// const uniqueDates = paginatedData
	//     .map((item) => item.date)
	//     .filter((date, idx, arr) => date && arr.indexOf(date) === idx);

	if (showLanding && !isAdmin) {
		return <PatientLanding onContinue={() => setShowLanding(false)} />;
	}

	return (
		<>
			{isLoading && <LoadingScreen />}

			<Dialog open={openDialog} fullWidth={true} maxWidth={"xs"} onClose={() => setOpenDialog(false)}>
				<DialogContent>
					<Grid
						container
						direction={"column"}
						sx={{ width: "100%" }}
						justifyContent={"center"}
						alignItems={"center"}
					>
						<Typography sx={{ fontSize: "18px", fontWeight: "bold", mb: 3 }}>
							Successfully Booked
						</Typography>
						<CheckCircleIcon sx={{ width: "100px", height: "100px", color: "green", mb: 2 }} />
						<Typography sx={{ fontSize: "14px" }} align="center">
							You have successfully booked your consultation for {selectedDay?.day || " - - -"},{" "}
							{selectedDay?.date ? convertDateToReadableFormat(selectedDay?.date) : "- - -"} between{" "}
							<span style={{ fontWeight: "bold" }}>{selectedSlot?.slot || " - - - "} AEST</span>
						</Typography>
						<Grid sx={{ mt: 2 }}>
							<Button
								sx={{ color: "green" }}
								onClick={() => {
									setOpenDialog(false), setSelectedDay(undefined);
								}}
							>
								Close
							</Button>
						</Grid>
					</Grid>
				</DialogContent>
			</Dialog>

			{showLanding && !isAdmin ? (
				<PatientLanding onContinue={() => setShowLanding(false)} />
			) : (
				<Grid container direction={"column"} sx={{ width: "100%", p: isDesktopOrTablet ? 2 : 0 }}>
					{/* Rest of the existing UI */}
					{isDesktopOrTablet ? (
						<Grid container sx={{ mt: 1, p: 2 }}>
							{/* Doctor selection dropdown - only show on admin page, not on public patient page */}
							{!externalSelectedDoctorId && !isPublicSchedulePage && isAdminSchedulePage && (
								<Grid container justifyContent="center" sx={{ mb: 3, width: "100%" }}>
									<FormControl sx={{ minWidth: 300 }}>
										<InputLabel id="doctor-select-label">Select a Doctor</InputLabel>
										<Select
											labelId="doctor-select-label"
											id="doctor-select"
											value={internalSelectedDoctorId || ""}
											onChange={handleDoctorChange}
											label="Select a Doctor"
											disabled={isLoading}
										>
											{doctors.map((doctor) => (
												<MenuItem key={doctor.id} value={doctor.id}>
													<Box
														sx={{
															display: "flex",
															alignItems: "center",
															gap: 2,
														}}
													>
														<DoctorImage
															doctorName={doctor.name}
															style={{
																width: "32px",
																height: "32px",
																borderRadius: "50%",
																objectFit: "cover",
															}}
														/>
														<Typography>{doctor.name}</Typography>
													</Box>
												</MenuItem>
											))}
										</Select>
										<FormHelperText>
											Please select a doctor to view available appointment slots
										</FormHelperText>
									</FormControl>
								</Grid>
							)}

							{/* Show availabilities for patients (public page) without doctor selection, or for admin with doctor selected */}
							{(isPublicSchedulePage || selectedDoctorId || (!isPublicSchedulePage && !isLoading)) && (
								<>
									<Grid
										container
										spacing={1}
										direction={"column"}
										size={{ lg: 4, xs: 12 }}
										alignItems={"center"}
									>
										{/* Desktop header section */}
										<Grid
											container
											direction="column"
											alignItems="center"
											sx={{ mb: 3, width: "100%" }}
										>
											<Typography
												variant="h5"
												sx={{
													fontWeight: "bold",
													textAlign: "center",
													color: "green",
													mt: 2,
												}}
											>
												{isPublicSchedulePage
													? "Available days"
													: selectedDoctorId
														? `Dr. ${filteredData.find((item) => item.doctorID === selectedDoctorId)?.doctorName || "Selected Doctor"}'s available days`
														: "Available days"}
											</Typography>
											<Typography sx={{ fontSize: "12px", color: "gray", mt: 1 }}>
												Filter by day
											</Typography>
										</Grid>
										{/* Desktop filter by day buttons */}
										<Grid
											container
											justifyContent={"start"}
											alignItems={"start"}
											sx={{ width: "100%", mb: 2 }}
										>
											<Button
												endIcon={
													filter === dayOfTheWeek.MONDAY ? (
														<CheckCircleIcon sx={{ width: "12px" }} />
													) : (
														<CircleOutlinedIcon sx={{ width: "12px" }} />
													)
												}
												sx={{
													fontSize: "12px",
													textTransform: "none",
													color: filter === dayOfTheWeek.MONDAY ? "green" : "grey",
												}}
												onClick={() => handleFilterChange(dayOfTheWeek.MONDAY)}
											>
												{dayOfTheWeek.MONDAY}
											</Button>
											<Button
												endIcon={
													filter === dayOfTheWeek.TUESDAY ? (
														<CheckCircleIcon sx={{ width: "12px" }} />
													) : (
														<CircleOutlinedIcon sx={{ width: "12px" }} />
													)
												}
												sx={{
													fontSize: "12px",
													textTransform: "none",
													color: filter === dayOfTheWeek.TUESDAY ? "green" : "grey",
												}}
												onClick={() => handleFilterChange(dayOfTheWeek.TUESDAY)}
											>
												{dayOfTheWeek.TUESDAY}
											</Button>
											<Button
												endIcon={
													filter === dayOfTheWeek.WEDNESDAY ? (
														<CheckCircleIcon sx={{ width: "12px" }} />
													) : (
														<CircleOutlinedIcon sx={{ width: "12px" }} />
													)
												}
												sx={{
													fontSize: "12px",
													textTransform: "none",
													color: filter === dayOfTheWeek.WEDNESDAY ? "green" : "grey",
												}}
												onClick={() => handleFilterChange(dayOfTheWeek.WEDNESDAY)}
											>
												{dayOfTheWeek.WEDNESDAY}
											</Button>
											<Button
												endIcon={
													filter === dayOfTheWeek.THURSDAY ? (
														<CheckCircleIcon sx={{ width: "12px" }} />
													) : (
														<CircleOutlinedIcon sx={{ width: "12px" }} />
													)
												}
												sx={{
													fontSize: "12px",
													textTransform: "none",
													color: filter === dayOfTheWeek.THURSDAY ? "green" : "grey",
												}}
												onClick={() => handleFilterChange(dayOfTheWeek.THURSDAY)}
											>
												{dayOfTheWeek.THURSDAY}
											</Button>
											<Button
												endIcon={
													filter === dayOfTheWeek.FRIDAY ? (
														<CheckCircleIcon sx={{ width: "12px" }} />
													) : (
														<CircleOutlinedIcon sx={{ width: "12px" }} />
													)
												}
												sx={{
													fontSize: "12px",
													textTransform: "none",
													color: filter === dayOfTheWeek.FRIDAY ? "green" : "grey",
												}}
												onClick={() => handleFilterChange(dayOfTheWeek.FRIDAY)}
											>
												{dayOfTheWeek.FRIDAY}
											</Button>
										</Grid>

										{/* Desktop navigation buttons - only for desktop/tablet */}
										{isDesktopOrTablet && (
											<Grid container alignItems={"start"} sx={{ width: "100%", mb: 2 }}>
												<Grid>
													<Button
														onClick={() => handlePrevious()}
														disabled={(() => {
															if (currentPage === 0) return true;
															if (isAdmin) return false;
															for (let i = currentPage - 1; i >= 0; i--) {
																const pageRanges = filteredData.slice(
																	i * itemsPerPage,
																	(i + 1) * itemsPerPage
																);
																const rangesToCheck = pageRanges.slice(
																	0,
																	Math.min(5, pageRanges.length)
																);
																if (rangesToCheck.some(hasAvailableSlots)) return false;
															}
															return true;
														})()}
														sx={{
															fontSize: "10px",
															fontWeight: "bold",
															color: "green",
														}}
													>
														Previous
													</Button>
												</Grid>
												<div style={{ flexGrow: 1 }} />
												<Grid>
													<Button
														disabled={isDesktopNextDisabled}
														onClick={() => handleNext()}
														sx={{
															color: desktopNextButtonColor,
															fontSize: "10px",
															fontWeight: "bold",
														}}
													>
														Next
													</Button>
												</Grid>
											</Grid>
										)}

										<Grid container direction={"column"} sx={{ width: "100%" }}>
											{paginatedData.length > 0 ? (
												paginatedData.map((value) => {
													// Calculate availability
													const totalSlots =
														value.slots?.reduce(
															(total, slot) => total + slot.remaining,
															0
														) || 0;
													const isAvailable = !(
														totalSlots <= 0 ||
														(value.availability != null && value.availability <= 0)
													);
													const isSelected =
														selectedDay && selectedDay.range_id === value.range_id;

													return (
														<Fragment key={`${value.range_id}`}>
															<div
																style={{
																	cursor: (() => {
																		const totalSlots =
																			value.slots?.reduce(
																				(total, slot) => total + slot.remaining,
																				0
																			) || 0;
																		return isAdmin ||
																			!(
																				totalSlots <= 0 ||
																				(value.availability != null &&
																					value.availability <= 0)
																			)
																			? "pointer"
																			: "default";
																	})(),
																	width: "100%",
																}}
																onClick={() => {
																	const totalSlots =
																		value.slots?.reduce(
																			(total, slot) => total + slot.remaining,
																			0
																		) || 0;
																	if (
																		isAdmin ||
																		!(
																			totalSlots <= 0 ||
																			(value.availability != null &&
																				value.availability <= 0)
																		)
																	) {
																		handleDaySelected(value);
																	}
																}}
															>
																<Box
																	sx={{
																		mx: 2,
																		mb: 1,
																		boxShadow: "0px 2px 8px rgba(0,0,0,0.15)",
																		borderRadius: 2,
																		background: isSelected
																			? "#008000"
																			: isAvailable
																				? "white"
																				: "#CFCFCF",
																		p: 2,
																		transition: "box-shadow 0.2s",
																		border: isSelected ? "2px solid green" : "none",
																		position: "relative",
																	}}
																>
																	<Grid
																		container
																		sx={{
																			boxShadow: "none",
																			backgroundColor: "transparent",
																			borderRadius: 0,
																			p: 0,
																			border: "none",
																		}}
																	>
																		<Grid size={{ xs: 8 }}>
																			<Typography
																				sx={{
																					fontSize: "20px",
																					fontWeight: "500",
																					color: isSelected
																						? "white"
																						: "green",
																					display: "flex",
																					alignItems: "center",
																				}}
																			>
																				{value.day &&
																					dayOfTheWeek[
																						value.day.toUpperCase()
																					]}
																				{!isAvailable && (
																					<LockIcon
																						sx={{
																							color: "grey",
																							fontSize: "18px",
																							ml: 1,
																						}}
																					/>
																				)}
																			</Typography>
																			<Typography
																				sx={{
																					fontSize: "14px",
																					color: isSelected
																						? "white"
																						: "black",
																				}}
																			>
																				{convertDateToReadableFormat(
																					value.date
																				)}
																			</Typography>
																		</Grid>

																		<Grid
																			size={{ xs: 4 }}
																			container
																			justifyContent="flex-end"
																			alignItems="center"
																		>
																			<Typography
																				sx={{
																					fontSize: "12px",
																					fontWeight: "bold",
																					color: isSelected
																						? "white"
																						: "black",
																				}}
																			>
																				{isAvailable
																					? "AVAILABLE"
																					: "FULLY BOOKED"}
																			</Typography>
																		</Grid>
																	</Grid>
																</Box>
															</div>
														</Fragment>
													);
												})
											) : (
												<>
													<Grid
														container
														sx={{ width: "100%" }}
														alignItems={"center"}
														justifyContent={"center"}
													>
														<Typography sx={{ fontSize: "12px", mt: 5 }} align="center">
															Our schedule is currently fully booked due to high demand,
															and we're working hard to open up more availability soon.{" "}
															<br />
															<br />
															Please check back later, or feel free to reach out if you
															have any questions—we'd love to assist you!
														</Typography>
													</Grid>
												</>
											)}
										</Grid>
									</Grid>

									{isDesktopOrTablet && (
										<Grid container direction={"column"} sx={{ width: "100%" }} size={{ lg: 8 }}>
											<Grid
												sx={{ mb: 3, width: "100%" }}
												container
												direction={"column"}
												justifyContent={"center"}
												alignItems={"center"}
												spacing={1}
											>
												{/* <Typography>
                                    Book your appointment
                                </Typography>
                                <Typography sx={{ fontSize: '12px' }}>
                                    Select date and time for your next appointment
                                </Typography> */}
											</Grid>
											<Grid sx={{ overflow: "auto", height: "85vh" }}>
												{selectedDay?.slots && selectedDay?.slots.length > 0 ? (
													selectedDay?.slots?.map((value) => {
														const referenceDay = moment(selectedDay?.date).toDate();

														const { startUTC, endUTC } = convertAustralianRangeToUtc(
															value.slot,
															referenceDay
														);
														return (
															<Fragment key={`${value.id}`}>
																<Grid
																	sx={{ mb: 1, width: "100%" }}
																	container
																	justifyContent={"center"}
																	alignItems={"center"}
																	spacing={1}
																	key={`${value.id}`}
																>
																	<Grid size={{ lg: 4 }}>
																		<Button
																			fullWidth
																			disabled={value.remaining <= 0}
																			sx={{
																				border: "1px solid green",
																				p: 2,
																				display: "flex",
																				alignItems: "center",
																				justifyContent: "center",
																				backgroundColor:
																					value.remaining <= 0
																						? "#CFCFCF"
																						: "white",
																				opacity: 1,
																				"&:hover": {
																					backgroundColor:
																						value.remaining <= 0
																							? "#CFCFCF"
																							: "#f0f0f0",
																				},
																			}}
																			onClick={() =>
																				value.remaining > 0 &&
																				handleSlotSelected(value)
																			}
																		>
																			{!isAdmin && (
																				<Typography
																					sx={{
																						color: "green",
																						fontWeight: "bold",
																					}}
																				>
																					{convertUtcToClientTime(
																						startUTC,
																						clientTimeZone
																					)}
																					-
																					{convertUtcToClientTime(
																						endUTC,
																						clientTimeZone
																					)}
																				</Typography>
																			)}
																			{isAdmin && (
																				<Typography
																					sx={{
																						color: "green",
																						fontWeight: "bold",
																					}}
																				>
																					{value.slot}
																				</Typography>
																			)}
																		</Button>
																	</Grid>
																	<Grid
																		sx={{
																			border: "1px solid green",
																			backgroundColor: "green",
																			p: 2,
																			borderRadius: 1,
																		}}
																		container
																		size={{ lg: 4 }}
																		alignItems={"center"}
																		justifyContent={"center"}
																	>
																		<Typography
																			sx={{
																				color: "white",
																				fontWeight: "bold",
																			}}
																		>
																			Consults left: {value.remaining}
																		</Typography>
																	</Grid>
																</Grid>
																<Collapse
																	in={selectedSlot && selectedSlot.id === value.id}
																	timeout={300}
																>
																	<Grid
																		size={{ lg: 12 }}
																		container
																		justifyContent={"center"}
																		sx={{ mb: 3 }}
																	>
																		<Box>
																			<Button
																				fullWidth
																				sx={{
																					background: selectedSlot
																						? "green"
																						: "#CFCFCF",
																					color: "white",
																					fontWeight: 700,
																					fontSize: "1rem",
																					borderRadius: 1,
																					py: 1.5,
																					px: 4,
																					boxShadow: selectedSlot
																						? "0px 2px 8px rgba(0,128,0,0.10)"
																						: "none",
																					transition: "background 0.2s",
																					maxWidth: 250,
																					margin: "0 auto",
																					display: "block",
																					textTransform: "none",
																				}}
																				disabled={!selectedSlot}
																				onClick={() => {
																					if (value.remaining > 0) {
																						handleSlotConfirmed();
																					}
																				}}
																			>
																				Continue
																			</Button>
																		</Box>
																	</Grid>
																</Collapse>
															</Fragment>
														);
													})
												) : (
													<>
														{selectedDay && (
															<Grid
																container
																sx={{ width: "100%", mt: 10 }}
																direction={"column"}
																justifyContent={"center"}
																alignItems={"center"}
															>
																<Grid
																	sx={{
																		p: 3,
																		borderRadius: 2,
																		border: "1px solid green",
																	}}
																>
																	<Typography
																		sx={{ color: "green", fontWeight: "bold" }}
																	>
																		ALL CONSULTATION APPOINTMENTS ARE FULLY BOOKED
																		FOR {selectedDay?.day?.toUpperCase()}{" "}
																		{convertDateToReadableFormat(
																			selectedDay?.date
																		)?.toUpperCase()}
																		.{" "}
																	</Typography>
																</Grid>
															</Grid>
														)}
													</>
												)}
											</Grid>
										</Grid>
									)}
								</>
							)}
						</Grid>
					) : (
						<>
							{!hideMobileHeader && (
								<Box
									sx={{
										width: "100%",
										background: "#007F00",
										py: 3,
										display: { xs: "block", md: "none" },
										textAlign: "center",
									}}
								>
									<Typography
										variant="h4"
										sx={{
											color: "white",
											fontWeight: 700,
											letterSpacing: 1,
											display: "inline",
										}}
									>
										Zenith
										<span
											style={{
												fontWeight: 400,
												color: "white",
												display: "inline",
											}}
										>
											Clinics
										</span>
									</Typography>
									<Typography
										sx={{
											color: "white",
											fontSize: 14,
											letterSpacing: 4,
											mt: 0,
										}}
									>
										Clinic & Dispensary
									</Typography>
								</Box>
							)}
							<Box
								sx={{
									pb: 10,
									background: hideMobileHeader ? "transparent" : "#f6f6f6",
									minHeight: "100vh",
								}}
							>
								{/* Book Appointment Header for mobile */}
								<Grid container direction="column" alignItems="center" sx={{ mb: 3, width: "100%" }}>
									<Typography variant="h4" sx={{ fontWeight: "bold", textAlign: "center", mt: 2 }}>
										Book Your <span style={{ color: "green" }}>Appointment</span>
									</Typography>
									<Typography sx={{ fontSize: "16px", color: "gray", mt: 1 }}>
										Select date and time for your appointment.
									</Typography>
								</Grid>

								{/* Doctor selection dropdown for mobile - only show on admin pages */}
								{!externalSelectedDoctorId && !isPublicSchedulePage && isAdminSchedulePage && (
									<Grid container justifyContent="center" sx={{ mb: 3, px: 2 }}>
										<FormControl fullWidth>
											<InputLabel id="doctor-select-mobile-label">Select a Doctor</InputLabel>
											<Select
												labelId="doctor-select-mobile-label"
												id="doctor-select-mobile"
												value={internalSelectedDoctorId || ""}
												onChange={handleDoctorChange}
												label="Select a Doctor"
												disabled={isLoading}
											>
												{doctors.map((doctor) => (
													<MenuItem key={doctor.id} value={doctor.id}>
														<Box
															sx={{
																display: "flex",
																alignItems: "center",
																gap: 2,
															}}
														>
															<DoctorImage
																doctorName={doctor.name}
																style={{
																	width: "32px",
																	height: "32px",
																	borderRadius: "50%",
																	objectFit: "cover",
																}}
															/>
															<Typography>{doctor.name}</Typography>
														</Box>
													</MenuItem>
												))}
											</Select>
											<FormHelperText>
												Please select a doctor to view available appointment slots
											</FormHelperText>
										</FormControl>
									</Grid>
								)}
								{/* Add horizontal day selector for mobile */}
								<Grid
									container
									justifyContent="center"
									alignItems="center"
									sx={{ mb: 3, width: "100%" }}
								>
									<Grid
										container
										alignItems="center"
										justifyContent="center"
										sx={{ width: "100%", maxWidth: "400px" }}
									>
										<IconButton
											onClick={() => handlePrevious()}
											disabled={currentPage === 0}
											sx={{ color: "green" }}
										>
											<NavigateBeforeIcon />
										</IconButton>
										<Grid container direction="column" alignItems="center" sx={{ flex: 1 }}>
											<Box sx={{ width: "100%" }}>
												{/* Day initials row */}
												<Box
													sx={{
														display: "flex",
														justifyContent: "space-between",
														mb: 0.5,
													}}
												>
													{weekDays.map((dayLabel, _idx) => (
														<Typography
															key={dayLabel}
															sx={{
																fontSize: "14px",
																fontWeight: "bold",
																width: "48px",
																textAlign: "center",
																color: "#888",
															}}
														>
															{dayLabel[0]}
														</Typography>
													))}
												</Box>
												{/* Date circles row */}
												<Box
													sx={{
														display: "flex",
														justifyContent: "space-between",
													}}
												>
													{weekDays.map((dayLabel, idx) => {
														let dayNumber = "";
														let dateStr = "";
														if (baseMonday) {
															const date = new Date(baseMonday);
															date.setDate(baseMonday.getDate() + idx);
															dayNumber = date.getDate().toString();
															dateStr = date.toISOString().split("T")[0];
														}
														const value = paginatedData.find((v) => v.date === dateStr);
														const totalSlots =
															value?.slots?.reduce(
																(total, slot) => total + slot.remaining,
																0
															) || 0;
														const isFullyBooked =
															!value ||
															totalSlots <= 0 ||
															(value.availability != null && value.availability <= 0);
														const isSelected = filterDate === dateStr && !isFullyBooked;
														return (
															<Button
																key={dateStr + dayLabel}
																onClick={() =>
																	setFilterDate(isSelected ? null : dateStr)
																}
																sx={{
																	minWidth: 0,
																	width: "48px",
																	height: "48px",
																	borderRadius: "50%",
																	padding: 0,
																	backgroundColor: isSelected
																		? "green"
																		: isFullyBooked
																			? "#CFCFCF"
																			: "white",
																	border: `2px solid ${isSelected ? "green" : isFullyBooked ? "#CFCFCF" : "green"}`,
																	color: isSelected
																		? "white"
																		: isFullyBooked
																			? "gray"
																			: "green",
																	fontWeight: 700,
																	display: "flex",
																	alignItems: "center",
																	justifyContent: "center",
																	fontSize: 16,
																	mx: 0.5,
																	"&:hover": {
																		backgroundColor: isFullyBooked
																			? "#CFCFCF"
																			: isSelected
																				? "green"
																				: "#f0f0f0",
																	},
																}}
																disabled={isFullyBooked}
															>
																<span style={{ fontSize: 16 }}>{dayNumber}</span>
															</Button>
														);
													})}
												</Box>
											</Box>
										</Grid>
										<IconButton
											onClick={() => handleNext()}
											disabled={isMobileNextDisabled}
											sx={{ color: mobileNextButtonColor }}
										>
											<NavigateNextIcon />
										</IconButton>
									</Grid>
								</Grid>
								{/* End horizontal day selector for mobile */}
								<Grid container direction={"column"} sx={{ width: "100%" }}>
									{(filterDate ? paginatedData.filter((v) => v.date === filterDate) : paginatedData)
										.length > 0 ? (
										(filterDate
											? paginatedData.filter((v) => v.date === filterDate)
											: paginatedData
										).map((value) => {
											const totalSlots =
												value.slots?.reduce((total, slot) => total + slot.remaining, 0) || 0;
											const isAvailable = !(
												totalSlots <= 0 ||
												(value.availability != null && value.availability <= 0)
											);
											const isSelected = selectedDay && selectedDay.range_id === value.range_id;
											return (
												<Fragment key={`${value.range_id}`}>
													<Box
														sx={{
															mx: 2,
															mb: 2,
															boxShadow: "0px 2px 8px rgba(0,0,0,0.15)",
															borderRadius: 2,
															background: isSelected
																? "white"
																: isAvailable
																	? "white"
																	: "#CFCFCF",
															p: 0,
															transition: "box-shadow 0.2s",
															border: isSelected ? "2px solid green" : "none",
															position: "relative",
														}}
													>
														<Grid
															container
															alignItems="center"
															sx={{
																p: 2,
																pb: isSelected ? 0 : 2,
																cursor: isAvailable ? "pointer" : "default",
																borderRadius: 2,
																WebkitTapHighlightColor: "transparent",
																outline: "none",
																"&:focus": { outline: "none" },
																"&:active": { outline: "none" },
															}}
															onClick={() => isAvailable && handleDaySelected(value)}
														>
															<Grid size={{ xs: 8 }}>
																<Typography
																	sx={{
																		fontSize: "22px",
																		fontWeight: 700,
																		color: isAvailable ? "green" : "#4d4d4d",
																		display: "flex",
																		alignItems: "center",
																	}}
																>
																	{value.day && dayOfTheWeek[value.day.toUpperCase()]}
																	{!isAvailable && (
																		<LockIcon
																			sx={{
																				color: "grey",
																				fontSize: "18px",
																				ml: 1,
																			}}
																		/>
																	)}
																</Typography>
																<Typography
																	sx={{
																		fontSize: "15px",
																		color: "#555",
																		mt: 0.5,
																	}}
																>
																	{convertDateToReadableFormat(value.date)}
																</Typography>
															</Grid>
															<Grid size={{ xs: 4 }}>
																<Typography
																	sx={{
																		fontSize: "15px",
																		fontWeight: 700,
																		color: isAvailable ? "green" : "#555",
																	}}
																>
																	{isAvailable ? "AVAILABLE" : "FULLY BOOKED"}
																</Typography>
															</Grid>
														</Grid>
														{isSelected && isAvailable && (
															<Box sx={{ p: 2, pt: 0 }}>
																<Box
																	sx={{
																		borderBottom: "1px solid #e0e0e0",
																		mb: 2,
																		mt: 1,
																	}}
																/>
																{/* Slots */}
																{value.slots && value.slots.length > 0 ? (
																	value.slots.map((slot) => {
																		const isSlotSelected =
																			selectedSlot && selectedSlot.id === slot.id;
																		const isSlotAvailable = slot.remaining > 0;
																		return (
																			<Box key={slot.id} sx={{ mb: 2 }}>
																				<Button
																					fullWidth
																					variant={
																						isSlotSelected
																							? "contained"
																							: "outlined"
																					}
																					sx={{
																						fontSize: "2rem",
																						fontWeight: 700,
																						borderRadius: 2,
																						border: "2px solid green",
																						color: isSlotSelected
																							? "white"
																							: "green",
																						background: isSlotSelected
																							? "green"
																							: "white",
																						mb: 0.5,
																						py: 1.5,
																						boxShadow: isSlotSelected
																							? "0px 2px 8px rgba(0,128,0,0.10)"
																							: "none",
																						"&:hover": {
																							background: isSlotAvailable
																								? isSlotSelected
																									? "green"
																									: "#f6fff6"
																								: "#CFCFCF",
																						},
																					}}
																					disabled={!isSlotAvailable}
																					onClick={() =>
																						isSlotAvailable &&
																						handleSlotSelected(slot)
																					}
																				>
																					{slot.slot}
																				</Button>
																				<Typography
																					sx={{
																						textAlign: "center",
																						color: "#555",
																						fontSize: "15px",
																						mb: 1,
																					}}
																				>
																					{slot.remaining} Consult
																					{slot.remaining === 1
																						? ""
																						: "s"}{" "}
																					Available
																				</Typography>
																			</Box>
																		);
																	})
																) : (
																	<Typography
																		sx={{
																			color: "green",
																			fontWeight: "bold",
																			textAlign: "center",
																			my: 2,
																		}}
																	>
																		ALL CONSULTATION APPOINTMENTS ARE FULLY BOOKED
																		FOR {value?.day?.toUpperCase()}{" "}
																		{convertDateToReadableFormat(
																			value?.date
																		)?.toUpperCase()}
																		.
																	</Typography>
																)}
																{/* Only show doctor info for admin pages, not for patients */}

																{isAdmin && (
																	<>
																		<Box
																			sx={{
																				borderBottom: "1px solid #e0e0e0",
																				my: 2,
																			}}
																		/>
																		{/* Doctor Info */}
																		<Box
																			sx={{
																				display: "flex",
																				alignItems: "center",
																				mt: 2,
																			}}
																		>
																			<DoctorImage
																				doctorName={selectedDay?.doctorName}
																				style={{
																					width: 48,
																					height: 48,
																					borderRadius: "50%",
																					marginRight: 12,
																					border: "2px solid #e0e0e0",
																				}}
																			/>
																			<Box>
																				<Typography
																					sx={{
																						color: "green",
																						fontWeight: 600,
																						fontSize: "15px",
																						lineHeight: 1,
																					}}
																				>
																					Meet Your Doctor
																				</Typography>
																				<Typography
																					sx={{
																						fontWeight: 700,
																						fontSize: "20px",
																						color: "#333",
																						lineHeight: 1.2,
																					}}
																				>
																					{selectedDay?.doctorName ||
																						"Selected Doctor"}
																				</Typography>
																			</Box>
																		</Box>
																	</>
																)}
															</Box>
														)}
													</Box>
												</Fragment>
											);
										})
									) : (
										<Grid
											container
											sx={{ width: "100%" }}
											alignItems={"center"}
											justifyContent={"center"}
										>
											<Typography sx={{ fontSize: "12px", mt: 5 }} align="center">
												Our schedule is currently fully booked due to high demand, and we're
												working hard to open up more availability soon. <br />
												<br />
												Please check back later, or feel free to reach out if you have any
												questions—we'd love to assist you!
											</Typography>
										</Grid>
									)}
								</Grid>
								{/* Continue Button Fixed at Bottom */}
								<Box sx={{ mx: 2, mt: 4, mb: 2 }}>
									<Button
										fullWidth
										sx={{
											background: selectedSlot ? "green" : "#CFCFCF",
											color: "white",
											fontWeight: 700,
											fontSize: "20px",
											borderRadius: 2,
											py: 1.5,
											boxShadow: selectedSlot ? "0px 2px 8px rgba(0,128,0,0.10)" : "none",
											transition: "background 0.2s",
										}}
										disabled={!selectedSlot}
										onClick={handleSlotConfirmed}
									>
										Continue
									</Button>
								</Box>
								<Typography
									sx={{
										color: "#888",
										fontSize: "14px",
										mt: 4,
										mb: 2,
										px: 3,
										textAlign: "left",
									}}
								>
									Any information you provide today is confidential and compliant with the Medical
									Board of Australia Good Medical Practice code, RACGP Standards of General Practice
									and our Medical Confidentiality Duty of Conduct for doctors in Australia, which
									means we protect your privacy and right to confidentiality
								</Typography>
								<Box
									sx={{
										borderTop: "1px solid #444",
										mt: 5,
										pt: 2,
										pb: 0,
										ml: 3,
										mr: 3,
										textAlign: "center",
										display: { xs: "block", md: "none" },
									}}
								>
									<Typography sx={{ fontSize: 18 }}>
										Provided by{" "}
										<Box
											component="span"
											sx={{
												color: "#007F00",
												fontWeight: 700,
												display: "inline",
											}}
										>
											ZenithClinics
										</Box>{" "}
										Pty Ltd
									</Typography>
								</Box>
							</Box>
						</>
					)}
				</Grid>
			)}
		</>
	);
};

export default Scheduler;
