import { But<PERSON>, <PERSON>lapse, Dialog, DialogContent, <PERSON>u, MenuItem, Typography, useMediaQuery, Box, IconButton } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Fragment, useEffect, useState } from "react";
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CircleOutlinedIcon from '@mui/icons-material/CircleOutlined';
import LockIcon from '@mui/icons-material/Lock';
// import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import { toZonedTime, format } from "date-fns-tz"; // Correct import

import { useTheme } from "@mui/material/styles"
import {
    convertDateToReadableFormat,
    // createTimeSlots
} from "../../../utils";
import { MakeGenerics, useMatch } from "@tanstack/react-location";
import { ApiClient } from "../../../services";
import { AvailableDate, Slot } from "../../../types";
import LoadingScreen from "../../../utils/loading-screen";

type DaysOfTheWeek = 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | ''

const dayOfTheWeek: { [key: string]: DaysOfTheWeek } = {
    MONDAY: 'Monday',
    TUESDAY: "Tuesday",
    WEDNESDAY: 'Wednesday',
    THURSDAY: 'Thursday',
    FRIDAY: 'Friday'
}

type UrlProps = MakeGenerics<{
    Params: {
        id: string; // This will be the zohoId
    }
}>

type WebSocketMessage = {
    type: string
    data: unknown
}

interface PatientData {
    patientID: string;
    // Add other patient fields as needed
}

const RebookNoShowPatient: React.FC<{ hideMobileHeader?: boolean }> = ({ hideMobileHeader }) => {
    const { params: { id } } = useMatch<UrlProps>();
   
    const [patientData, setPatientData] = useState<PatientData | null>(null);

    const [selectedDay, setSelectedDay] = useState<AvailableDate | undefined>(undefined)
    const [filter, setFilter] = useState<DaysOfTheWeek>('')
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const theme = useTheme()
    const isDesktopOrTablet = useMediaQuery(theme.breakpoints.up('md'));
    const [data, setData] = useState<AvailableDate[]>([])
    const [selectedSlot, setSelectedSlot] = useState<Slot | undefined>(undefined)
    const [openDialog, setOpenDialog] = useState(false)
    const [isLoading, setIsLoading] = useState(true)
    const [filteredData, setFilteredData] = useState<AvailableDate[]>([]);
    const [currentPage, setCurrentPage] = useState(0);
    const [restart, setRestart] = useState(true)

    const itemsPerPage = 5;

    // Fetch patient data
    useEffect(() => {
        const fetchPatientData = async () => {
            try {
               
                const data = await ApiClient.getPatientByZohoId(id);
               
                setPatientData(data);
            } catch (error) {
                console.error('Error fetching patient data:', error);
                // Show error message to user
                alert('Unable to your information. Please check your booking link and try again.');
            }
        };

        if (id) {
            fetchPatientData();
        }
    }, [id]);

    // Handle navigation
    const handleNext = () => {
        if ((currentPage + 1) * itemsPerPage < filteredData.length) {
            // Find the next page with available slots
            let nextPage = currentPage + 1;
            const maxPage = Math.ceil(filteredData.length / itemsPerPage) - 1;

            while (nextPage <= maxPage) {
                const pageRanges = filteredData.slice(nextPage * itemsPerPage, (nextPage + 1) * itemsPerPage);
                const rangesToCheck = pageRanges.slice(0, Math.min(5, pageRanges.length));

                if (rangesToCheck.some(hasAvailableSlots)) {
                    break; // Found a page with available slots
                }

                nextPage++;
            }

            // If we've gone past the last page, stay on the current page
            if (nextPage > maxPage) {
                nextPage = currentPage;
            }

            setCurrentPage(nextPage);
        }
    };

    const handlePrevious = () => {
        if (currentPage > 0) {
            // Find the previous page with available slots
            let prevPage = currentPage - 1;

            while (prevPage >= 0) {
                const pageRanges = filteredData.slice(prevPage * itemsPerPage, (prevPage + 1) * itemsPerPage);
                const rangesToCheck = pageRanges.slice(0, Math.min(5, pageRanges.length));

                if (rangesToCheck.some(hasAvailableSlots)) {
                    break; // Found a page with available slots
                }

                prevPage--;
            }

            // If we've gone before the first page, stay on the current page
            if (prevPage < 0) {
                prevPage = 0;
            }

            setCurrentPage(prevPage);
        }
    };

    const paginatedData = filteredData.slice(
        currentPage * itemsPerPage,
        (currentPage + 1) * itemsPerPage
    );

    const handleFilterChange = (day: DaysOfTheWeek) => {

        if (filter === day) {
            setFilter("");
            setFilteredData(data);

        } else {
            setFilter(day);
            setFilteredData(data.filter((item) => item.day === day));
        }

        setSelectedSlot(undefined)
        setCurrentPage(0)
        setAnchorEl(null)
        return;
    }

    const handleDaySelected = (value: AvailableDate) => {
        if (selectedDay && selectedDay.range_id === value.range_id) {
            setSelectedDay(undefined)
            return;
        }

        if (value.slots) {
            const sortedSlots = value.slots.sort((a, b) => {
                const startA = a.slot.split(' - ')[0];
                const startB = b.slot.split(' - ')[0];
                const timeA = new Date(`1970-01-01T${startA}:00Z`);
                const timeB = new Date(`1970-01-01T${startB}:00Z`);

                return timeA.getTime() - timeB.getTime();
            });

            setSelectedDay(() => {
                return {
                    ...value,
                    slots: sortedSlots,
                }
            });
        }
        setSelectedSlot(undefined)
        return;
    }

    // const handleMenu = (e: React.MouseEvent<HTMLElement>) => {
    //     setAnchorEl(e.currentTarget);
    // };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleSlotSelected = (slot: Slot) => {
        setSelectedSlot(slot)

    }

    const handleSlotConfirmed = async () => {
        setIsLoading(true)
        try {
            if (!patientData?.patientID) {
                alert('Missing Patient ID');
                setIsLoading(false)
                return;
            }

            if (!selectedSlot) {
                alert('Missing Patient Slot');
                setIsLoading(false)
                return;
            }
            const result = await ApiClient.postPatientBooking(id, selectedSlot, 'rebook')
            if (result) {
                result.forEach((newSlot) => {
                    setFilteredData((prev) =>
                        prev.map((day) => {
                            if (day.range_id === newSlot.range_id) {
                                const updatedSlots = day.slots?.map((slot) =>
                                    slot.id === newSlot.id
                                        ? { ...slot, remaining: newSlot.remaining }
                                        : slot
                                );

                                const updatedDay = {
                                    ...day,
                                    slots: updatedSlots
                                };

                                return updatedDay
                            }

                            return day;
                        })
                    );
                })
                setOpenDialog(true)
            }
        }
        catch (e) {
            alert('Thank you for registering! If you\'re unable to book your appointment, please contact our support team for assistance.')
            setIsLoading(false)
        }
        finally {
            setIsLoading(false)
        }
    }

    // Helper function to check if a range has available slots
    const hasAvailableSlots = (range: AvailableDate): boolean => {
        const totalSlots = range.slots?.reduce((total, slot) => total + slot.remaining, 0) || 0;
        return totalSlots > 0;
    }

    // Helper function to find the first page with available slots
    const findFirstPageWithAvailableSlots = (allData: AvailableDate[]): number => {
        const rangesPerPage = itemsPerPage;

        for (let i = 0; i < Math.ceil(allData.length / rangesPerPage); i++) {
            const pageRanges = allData.slice(i * rangesPerPage, (i + 1) * rangesPerPage);

            // Check if any of the first 5 ranges (or fewer if page has less than 5) have available slots
            const rangesToCheck = pageRanges.slice(0, Math.min(5, pageRanges.length));
            if (rangesToCheck.some(hasAvailableSlots)) {
                return i;
            }
        }

        // If no page has available slots, return the first page
        return 0;
    }

    useEffect(() => {
        const fetchData = async () => {
            const timeZone = "Australia/Sydney";

            try {
                const result = await ApiClient.getAvailabilitiesForPatient()
                const parsedStartDate = format(toZonedTime(new Date(), timeZone), "yyyy-MM-dd'T'HH:mm:ssXXX", { timeZone });
                const currentAusTime = parsedStartDate.split('T')[0]
                const filteredData = result.filter(d => d.date === currentAusTime)

                setData(() => {
                    return [...filteredData]
                })

                // Find the first page with available slots
                const firstAvailablePage = findFirstPageWithAvailableSlots(filteredData);
                setFilteredData(filteredData)
                setCurrentPage(firstAvailablePage)

                // Try to find today's slots with availability first
                const todaySlots = result.find(d => d.date === currentAusTime && hasAvailableSlots(d)) ||
                                   result.find(d => d.date === currentAusTime);

                if (todaySlots?.slots) {
                    const sortedSlots = todaySlots.slots.sort((a, b) => {
                        const startA = a.slot.split(' - ')[0];
                        const startB = b.slot.split(' - ')[0];
                        const timeA = new Date(`1970-01-01T${startA}:00Z`);
                        const timeB = new Date(`1970-01-01T${startB}:00Z`);

                        return timeA.getTime() - timeB.getTime();
                    });

                    setSelectedDay(() => {
                        return {
                            ...todaySlots,
                            slots: sortedSlots,
                        }
                    });
                }
            }
            catch (e) {
                const error = e as any
                alert(error.message)
            }
            finally {
                setIsLoading(false)
            }
        }

        fetchData()
    }, [])

    useEffect(() => {
        const ws = new WebSocket(`${import.meta.env.VITE_WSS}`)
        ws.onmessage = (event) => {
            const message = JSON.parse(event.data) as WebSocketMessage

            if (message.type === 'new_booking') {
                const slotForSelectedDay: Slot[] = []
                const data = message.data as Slot[]
                if (data.length > 0) {
                    data.forEach((newSlot) => {
                        setFilteredData((prev) =>
                            prev.map((day) => {
                                if (day.range_id === newSlot.range_id) {
                                    const updatedSlots = day.slots?.map((slot) =>
                                        slot.id === newSlot.id
                                            ? { ...slot, remaining: newSlot.remaining }
                                            : slot
                                    );

                                    const updatedDay = {
                                        ...day,
                                        slots: updatedSlots
                                    };

                                    return updatedDay
                                }

                                return day;
                            })
                        );

                        if (newSlot.range_id === selectedDay?.range_id) {
                            slotForSelectedDay.push(newSlot)
                        }
                    })
                }
                if (slotForSelectedDay.length > 0 && selectedDay?.slots) {
                    const combinedSlot = [
                        ...new Map(
                            [...selectedDay.slots, ...slotForSelectedDay].map(slot => [slot.id, slot])
                        ).values()
                    ];
                    const sortedSlots = combinedSlot.sort((a, b) => {
                        const startA = a.slot.split(' - ')[0];
                        const startB = b.slot.split(' - ')[0];
                        const timeA = new Date(`1970-01-01T${startA}:00Z`);
                        const timeB = new Date(`1970-01-01T${startB}:00Z`);

                        return timeA.getTime() - timeB.getTime();
                    });

                    setSelectedDay(() => {
                        return {
                            ...selectedDay,
                            slots: sortedSlots,
                        }
                    });
                }
            }
        }

        ws.onclose = () => {
            setRestart(!restart)
        };

        ws.onerror = (error) => {
            console.log('ws error:', error);
        };
    }, [restart])

    return (
        <>
            <Dialog open={openDialog} fullWidth={true} maxWidth={'xs'} onClose={() => setOpenDialog(false)}>
                <DialogContent>
                    <Grid container direction={'column'} sx={{ width: '100%' }} justifyContent={'center'} alignItems={'center'}>
                        <Typography sx={{ fontSize: '18px', fontWeight: 'bold', mb: 3 }}>
                            Successfully Booked
                        </Typography>
                        <CheckCircleIcon sx={{ width: '100px', height: '100px', color: 'green', mb: 2 }} />
                        <Typography sx={{ fontSize: '14px' }} align="center">
                            You have successfully booked your consultation for {selectedDay?.day || " - - -"}, {selectedDay?.date ? convertDateToReadableFormat(selectedDay?.date) : '- - -'} between <span style={{ fontWeight: 'bold' }}>{selectedSlot?.slot || ' - - - '} AEST</span>
                        </Typography>
                        <Grid sx={{ mt: 2 }}>
                            <Button sx={{ color: 'green' }} onClick={() => {
                                setOpenDialog(false),
                                    setSelectedDay(undefined)
                            }}>
                                Close
                            </Button>
                        </Grid>
                    </Grid>
                </DialogContent>
            </Dialog>
            {isLoading && <LoadingScreen />}
            {isDesktopOrTablet ? (
                <Grid container sx={{ mt: 1, p: 2 }}>
                    <Grid
                        container
                        spacing={1}
                        direction={'column'}
                        size={{ lg: 4, xs: 12 }}
                        alignItems={'center'}
                    >
                        {/* New header section */}
                        <Grid container direction="column" alignItems="center" sx={{ mb: 3, width: '100%' }}>
                            <Typography variant="h4" sx={{ fontWeight: 'bold', textAlign: 'center' }}>
                                Book Your <span style={{ color: 'green' }}>Appointment</span>
                            </Typography>
                            <Typography sx={{ fontSize: '16px', color: 'gray', mt: 1 }}>
                                Select date and time for your appointment.
                            </Typography>
                        </Grid>

                        {isDesktopOrTablet ? (
                            <>
                                <Grid>
                                    <Typography sx={{ fontSize: '12px', color: 'grey' }}>
                                        Filter by day
                                    </Typography>
                                </Grid>

                                <Grid container justifyContent={'start'} alignItems={'start'} sx={{ width: '100%', mb: 2 }}>
                                    <Button
                                        endIcon={filter === dayOfTheWeek.MONDAY ? <CheckCircleIcon sx={{ width: '12px' }} /> : <CircleOutlinedIcon sx={{ width: '12px' }} />}
                                        sx={{ fontSize: '12px', textTransform: 'none', color: filter === dayOfTheWeek.MONDAY ? 'green' : 'grey' }}
                                        onClick={() => handleFilterChange(dayOfTheWeek.MONDAY)}
                                    >
                                        {dayOfTheWeek.MONDAY}
                                    </Button>
                                    <Button
                                        endIcon={filter === dayOfTheWeek.TUESDAY ? <CheckCircleIcon sx={{ width: '12px' }} /> : <CircleOutlinedIcon sx={{ width: '12px' }} />}
                                        sx={{ fontSize: '12px', textTransform: 'none', color: filter === dayOfTheWeek.TUESDAY ? 'green' : 'grey' }}
                                        onClick={() => handleFilterChange(dayOfTheWeek.TUESDAY)}
                                    >
                                        {dayOfTheWeek.TUESDAY}
                                    </Button>
                                    <Button
                                        endIcon={filter === dayOfTheWeek.WEDNESDAY ? <CheckCircleIcon sx={{ width: '12px' }} /> : <CircleOutlinedIcon sx={{ width: '12px' }} />}
                                        sx={{ fontSize: '12px', textTransform: 'none', color: filter === dayOfTheWeek.WEDNESDAY ? 'green' : 'grey' }}
                                        onClick={() => handleFilterChange(dayOfTheWeek.WEDNESDAY)}
                                    >
                                        {dayOfTheWeek.WEDNESDAY}
                                    </Button>
                                    <Button
                                        endIcon={filter === dayOfTheWeek.THURSDAY ? <CheckCircleIcon sx={{ width: '12px' }} /> : <CircleOutlinedIcon sx={{ width: '12px' }} />}
                                        sx={{ fontSize: '12px', textTransform: 'none', color: filter === dayOfTheWeek.THURSDAY ? 'green' : 'grey' }}
                                        onClick={() => handleFilterChange(dayOfTheWeek.THURSDAY)}
                                    >
                                        {dayOfTheWeek.THURSDAY}
                                    </Button>
                                    <Button
                                        endIcon={filter === dayOfTheWeek.FRIDAY ? <CheckCircleIcon sx={{ width: '12px' }} /> : <CircleOutlinedIcon sx={{ width: '12px' }} />}
                                        sx={{ fontSize: '12px', textTransform: 'none', color: filter === dayOfTheWeek.FRIDAY ? 'green' : 'grey' }}
                                        onClick={() => handleFilterChange(dayOfTheWeek.FRIDAY)}
                                    >
                                        {dayOfTheWeek.FRIDAY}
                                    </Button>
                                </Grid>
                            </>
                        ) : (
                            <>
                                {/* Day selector with circular buttons for mobile */}
                                <Grid container justifyContent="center" alignItems="center" sx={{ mb: 3, width: '100%' }}>

                                    {/* Day selector with navigation */}
                                    <Grid container alignItems="center" justifyContent="center" sx={{ width: '100%', maxWidth: '400px' }}>
                                        <IconButton
                                            onClick={() => handlePrevious()}
                                            disabled={currentPage === 0}
                                            sx={{ color: 'green' }}
                                        >
                                            <NavigateBeforeIcon />
                                        </IconButton>

                                        <Grid container direction="column" alignItems="center" sx={{ flex: 1 }}>
                                            {/* Combined day labels and date circles */}
                                            <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
                                                <Box sx={{ display: 'flex', flexDirection: 'column', width: '80%' }}>
                                                    {/* Day labels in a single row */}
                                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                                                        {['M', 'T', 'W', 'T', 'F'].map((day, index) => (
                                                            <Typography key={index} sx={{ fontSize: '14px', fontWeight: 'bold', width: '40px', textAlign: 'center' }}>
                                                                {day}
                                                            </Typography>
                                                        ))}
                                                    </Box>

                                                    {/* Date circles in a single row */}
                                                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                                        {paginatedData.slice(0, 5).map((value) => {
                                                            const date = value.date ? new Date(value.date) : new Date();
                                                            const day = date.getDate();
                                                            const totalSlots = value.slots?.reduce((total, slot) => total + slot.remaining, 0) || 0;
                                                            const isAvailable = totalSlots > 0 && (value.availability == null || value.availability > 0);
                                                            const isSelected = selectedDay && selectedDay.range_id === value.range_id;

                                                            return (
                                                                <Button
                                                                    key={value.range_id}
                                                                    onClick={() => {
                                                                        if (isAvailable) {
                                                                            handleDaySelected(value);
                                                                        }
                                                                    }}
                                                                    sx={{
                                                                        minWidth: 0,
                                                                        width: '40px',
                                                                        height: '40px',
                                                                        borderRadius: '50%',
                                                                        padding: 0,
                                                                        backgroundColor: isSelected ? 'green' : isAvailable ? 'white' : '#CFCFCF',
                                                                        border: '1px solid',
                                                                        borderColor: isSelected ? 'green' : isAvailable ? 'green' : '#CFCFCF',
                                                                        color: isSelected ? 'white' : isAvailable ? 'green' : 'gray',
                                                                        '&:hover': {
                                                                            backgroundColor: isAvailable ? (isSelected ? 'green' : '#f0f0f0') : '#CFCFCF',
                                                                        },
                                                                    }}
                                                                >
                                                                    {day}
                                                                </Button>
                                                            );
                                                        })}
                                                    </Box>
                                                </Box>
                                            </Box>
                                        </Grid>

                                        <IconButton
                                            onClick={() => handleNext()}
                                            disabled={currentPage >= Math.ceil(filteredData.length / itemsPerPage) - 1}
                                            sx={{ color: 'green' }}
                                        >
                                            <NavigateNextIcon />
                                        </IconButton>
                                    </Grid>
                                </Grid>

                                {/* Hidden menu for filtering - keep for compatibility */}
                                <Menu
                                    id="menu-appbar"
                                    anchorEl={anchorEl}
                                    anchorOrigin={{
                                        vertical: 'bottom',
                                        horizontal: 'right',
                                    }}
                                    keepMounted
                                    transformOrigin={{
                                        vertical: 'top',
                                        horizontal: 'right',
                                    }}
                                    open={Boolean(anchorEl)}
                                    onClose={handleClose}
                                >
                                    <MenuItem onClick={() => handleFilterChange(dayOfTheWeek.MONDAY)} sx={{ fontSize: '14px', textTransform: 'none', color: filter === dayOfTheWeek.MONDAY ? 'green' : 'grey' }}>
                                        {dayOfTheWeek.MONDAY}
                                        {filter === dayOfTheWeek.MONDAY ? <CheckCircleIcon sx={{ width: '14px', ml: 2 }} /> : <CircleOutlinedIcon sx={{ width: '14px', ml: 2 }} />}
                                    </MenuItem>
                                    <MenuItem onClick={() => handleFilterChange(dayOfTheWeek.TUESDAY)} sx={{ fontSize: '14px', textTransform: 'none', color: filter === dayOfTheWeek.TUESDAY ? 'green' : 'grey' }}>
                                        {dayOfTheWeek.TUESDAY}
                                        {filter === dayOfTheWeek.TUESDAY ? <CheckCircleIcon sx={{ width: '14px', ml: 2 }} /> : <CircleOutlinedIcon sx={{ width: '14px', ml: 2 }} />}
                                    </MenuItem>
                                    <MenuItem onClick={() => handleFilterChange(dayOfTheWeek.WEDNESDAY)} sx={{ fontSize: '14px', textTransform: 'none', color: filter === dayOfTheWeek.WEDNESDAY ? 'green' : 'grey' }}>
                                        {dayOfTheWeek.WEDNESDAY}
                                        {filter === dayOfTheWeek.WEDNESDAY ? <CheckCircleIcon sx={{ width: '14px', ml: 2 }} /> : <CircleOutlinedIcon sx={{ width: '14px', ml: 2 }} />}
                                    </MenuItem>
                                    <MenuItem onClick={() => handleFilterChange(dayOfTheWeek.THURSDAY)} sx={{ fontSize: '14px', textTransform: 'none', color: filter === dayOfTheWeek.THURSDAY ? 'green' : 'grey' }}>
                                        {dayOfTheWeek.THURSDAY}
                                        {filter === dayOfTheWeek.THURSDAY ? <CheckCircleIcon sx={{ width: '14px', ml: 2 }} /> : <CircleOutlinedIcon sx={{ width: '14px', ml: 2 }} />}
                                    </MenuItem>
                                    <MenuItem onClick={() => handleFilterChange(dayOfTheWeek.FRIDAY)} sx={{ fontSize: '14px', textTransform: 'none', color: filter === dayOfTheWeek.FRIDAY ? 'green' : 'grey' }}>
                                        {dayOfTheWeek.FRIDAY}
                                        {filter === dayOfTheWeek.FRIDAY ? <CheckCircleIcon sx={{ width: '14px', ml: 2 }} /> : <CircleOutlinedIcon sx={{ width: '14px', ml: 2 }} />}
                                    </MenuItem>
                                </Menu>
                            </>
                        )}

                        {/* Desktop navigation buttons - hide on mobile since we have the day selector */}
                        {isDesktopOrTablet && (
                            <Grid container alignItems={'start'} sx={{ width: '100%' }}>
                                <Grid>
                                    <Button
                                        onClick={() => handlePrevious()}
                                        disabled={currentPage === 0}
                                        sx={{ fontSize: '10px', fontWeight: 'bold', color: 'green' }}>
                                        Previous
                                    </Button>
                                </Grid>
                                <div style={{ flexGrow: 1 }} />
                                <Grid>
                                    <Button
                                        disabled={currentPage >= Math.ceil(filteredData.length / itemsPerPage) - 1}
                                        onClick={() => handleNext()}
                                        sx={{ fontSize: '10px', fontWeight: 'bold', color: 'green', }}>
                                        Next
                                    </Button>
                                </Grid>
                            </Grid>
                        )}

                        <Grid container direction={'column'} sx={{ width: '100%' }}>
                            {paginatedData.length > 0 ? paginatedData.map((value) => {
                                // Calculate availability
                                const totalSlots = value.slots?.reduce((total, slot) => total + slot.remaining, 0) || 0;
                                const isAvailable = !(totalSlots <= 0 || (value.availability != null && value.availability <= 0));
                                const isSelected = selectedDay && selectedDay.range_id === value.range_id;

                                return (
                                    <Fragment key={`${value.range_id}`}>
                                        <div
                                            style={{
                                                cursor: isAvailable ? 'pointer' : 'default',
                                                width: '100%',
                                                marginBottom: '10px',
                                            }}
                                            onClick={() => {
                                                if (isAvailable) {
                                                    handleDaySelected(value);
                                                }
                                            }}
                                        >
                                            <Grid container
                                                sx={{
                                                    boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.2)",
                                                    backgroundColor: isAvailable ? 'white' : '#CFCFCF',
                                                    borderRadius: 1,
                                                    p: 2,
                                                    border: isSelected ? '2px solid green' : 'none',
                                                }}
                                            >
                                                <Grid size={{ xs: 8 }}>
                                                    <Typography sx={{
                                                        fontSize: '20px',
                                                        fontWeight: '500',
                                                        color: 'green',
                                                        display: 'flex',
                                                        alignItems: 'center'
                                                    }}>
                                                        {value.day && dayOfTheWeek[value.day.toUpperCase()]}
                                                        {!isAvailable && (
                                                            <LockIcon sx={{ color: 'grey', fontSize: '18px', ml: 1 }} />
                                                        )}
                                                    </Typography>
                                                    <Typography sx={{ fontSize: '14px', color: 'black' }}>
                                                        {convertDateToReadableFormat(value.date)}
                                                    </Typography>
                                                </Grid>

                                                <Grid size={{ xs: 4 }} container justifyContent="flex-end" alignItems="center">
                                                    <Typography sx={{
                                                        fontSize: '12px',
                                                        fontWeight: 'bold',
                                                        color: isAvailable ? 'green' : 'black'
                                                    }}>
                                                        {isAvailable ? 'AVAILABLE' : 'FULLY BOOKED'}
                                                    </Typography>
                                                </Grid>
                                            </Grid>
                                        </div>
                                    </Fragment>
                                )
                            }) :
                                <>
                                    <Grid container sx={{ width: '100%' }} alignItems={'center'} justifyContent={'center'}>
                                        <Typography sx={{ fontSize: '12px', mt: 5 }} align="center">
                                            Our schedule is currently fully booked due to high demand, and we're working hard to open up more availability soon. <br /><br />Please check back later, or feel free to reach out if you have any questions—we'd love to assist you!
                                        </Typography>
                                    </Grid>
                                </>}
                        </Grid>
                    </Grid>

                    {isDesktopOrTablet &&
                        <Grid container direction={'column'} sx={{ width: '100%' }} size={{ lg: 8 }}>
                            <Grid sx={{ mb: 3, width: '100%' }} container direction={'column'} justifyContent={'center'} alignItems={'center'} spacing={1}>
                                <Typography>
                                    Book your appointment
                                </Typography>
                                <Typography sx={{ fontSize: '12px', fontWeight: 700 }}>
                                    SELECT YOUR AVAILABLE TIME TODAY
                                </Typography>
                            </Grid>
                            <Grid sx={{ width: '100%', p: 2 }} container justifyContent={'center'}>
                            <Button variant='contained' sx={{ width: '50%' }} onClick={() => {
                                // window.location.href = url;
                                if (patientData?.patientID) {
                                    const baseUrl = `${window.location.origin}/waiting-room`;
                                    const url = `${baseUrl}?token=${patientData.patientID}`;
                                    console.log(url)
                                    window.location.href = url;
                                }
                            }}>
                                Join Now
                            </Button>
                        </Grid>
                            <Grid sx={{ overflow: 'auto', height: '85vh' }}>
                                {
                                    selectedDay?.slots &&
                                        selectedDay?.slots.length > 0 ?
                                        selectedDay?.slots?.map((value) => {
                                            return (
                                                <Fragment key={`${value.id}`}>
                                                    <Grid
                                                        sx={{ mb: 1, width: '100%' }}
                                                        container
                                                        justifyContent={'center'}
                                                        alignItems={'center'}
                                                        spacing={1}
                                                        key={`${value.id}`}
                                                    >
                                                        <Grid size={{ lg: 4 }}>
                                                            <Button
                                                                fullWidth
                                                                disabled={value.remaining <= 0}
                                                                sx={{
                                                                    border: '1px solid green',
                                                                    p: 2,
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    backgroundColor: value.remaining <= 0 ? '#CFCFCF' : 'white',
                                                                    opacity: 1,
                                                                    '&:hover': {
                                                                        backgroundColor: value.remaining <= 0 ? '#CFCFCF' : '#f0f0f0',
                                                                    },
                                                                }}
                                                                onClick={() => value.remaining > 0 && handleSlotSelected(value)}
                                                            >
                                                                <Typography sx={{ color: 'green', fontWeight: 'bold' }}>
                                                                    {value.slot}
                                                                </Typography>
                                                            </Button>
                                                        </Grid>
                                                        <Grid
                                                            sx={{
                                                                border: '1px solid green',
                                                                backgroundColor: 'green',
                                                                p: 2,
                                                                borderRadius: 1,
                                                            }}
                                                            container
                                                            size={{ lg: 4 }}
                                                            alignItems={'center'}
                                                            justifyContent={'center'}
                                                        >
                                                            <Typography sx={{ color: 'white', fontWeight: 'bold' }}>
                                                                Available
                                                            </Typography>
                                                        </Grid>
                                                    </Grid>
                                                    <Collapse
                                                        in={selectedSlot && selectedSlot.id === value.id}
                                                        timeout={300}
                                                    >
                                                        <Grid
                                                            size={{ lg: 12 }}
                                                            container
                                                            justifyContent={'center'}
                                                            sx={{ mb: 3 }}
                                                        >
                                                            <Button
                                                                fullWidth
                                                                sx={{
                                                                    background: selectedSlot && selectedSlot.id === value.id ? 'green' : '#CFCFCF',
                                                                    color: 'white',
                                                                    fontWeight: 700,
                                                                    fontSize: '1rem',
                                                                    borderRadius: 1,
                                                                    py: 1.5,
                                                                    px: 4,
                                                                    boxShadow: selectedSlot && selectedSlot.id === value.id ? '0px 2px 8px rgba(0,128,0,0.10)' : 'none',
                                                                    transition: 'background 0.2s',
                                                                    maxWidth: 250,
                                                                    margin: '0 auto',
                                                                    display: 'block',
                                                                    textTransform: 'none',
                                                                }}
                                                                disabled={!(selectedSlot && selectedSlot.id === value.id)}
                                                                onClick={() => {
                                                                    if (selectedSlot && selectedSlot.id === value.id && value.remaining > 0) {
                                                                        handleSlotConfirmed();
                                                                    }
                                                                }}
                                                            >
                                                                Continue
                                                            </Button>
                                                        </Grid>
                                                    </Collapse>
                                                </Fragment>
                                            )
                                        }) :
                                        <>
                                            {selectedDay && <Grid container sx={{ width: '100%', mt: 10 }} direction={'column'} justifyContent={'center'} alignItems={'center'} >
                                                <Grid sx={{ p: 3, borderRadius: 2, border: '1px solid green' }}>
                                                    <Typography sx={{ color: 'green', fontWeight: 'bold' }}>
                                                        ALL CONSULTATION APPOINTMENTS ARE FULLY BOOKED FOR {selectedDay?.day?.toUpperCase()} {convertDateToReadableFormat(selectedDay?.date)?.toUpperCase()}.                                                </Typography>
                                                </Grid>
                                            </Grid>}
                                        </>
                                }
                            </Grid>
                        </Grid>
                    }
                </Grid>
            ) : (
                <>
                    {/* Mobile ZenithClinics header, only if not embedded */}
                    {!hideMobileHeader && (
                        <Box
                            sx={{
                                width: '100%',
                                background: '#007F00',
                                py: 3,
                                display: { xs: 'block', md: 'none' },
                                textAlign: 'center',
                            }}
                        >
                            <Typography variant="h4" sx={{ color: 'white', fontWeight: 700, letterSpacing: 1, display: 'inline' }}>
                                Zenith<span style={{ fontWeight: 400, color: 'white', display: 'inline' }}>Clinics</span>
                            </Typography>
                            <Typography sx={{ color: 'white', fontSize: 14, letterSpacing: 4, mt: 0 }}>
                                Clinic & Dispensary
                            </Typography>
                        </Box>
                    )}
                    {/* Main mobile content with conditional background */}
                    <Box sx={{ pb: 10, background: hideMobileHeader ? 'transparent' : '#f6f6f6', minHeight: '100vh' }}>
                        <Grid container direction="column" alignItems="center" sx={{ mb: 3, width: '100%' }}>
                            <Typography variant="h4" sx={{ fontWeight: 'bold', textAlign: 'center', mt: 2 }}>
                                Book Your <span style={{ color: 'green' }}>Appointment</span>
                            </Typography>
                            <Typography sx={{ fontSize: '16px', color: 'gray', mt: 1 }}>
                                Select date and time for your appointment.
                            </Typography>
                        </Grid>
                        {/* Day cards */}
                        <Grid container direction={'column'} sx={{ width: '100%' }}>
                            {paginatedData.length > 0 ? paginatedData.map((value) => {
                                const totalSlots = value.slots?.reduce((total, slot) => total + slot.remaining, 0) || 0;
                                const isAvailable = !(totalSlots <= 0 || (value.availability != null && value.availability <= 0));
                                const isSelected = selectedDay && selectedDay.range_id === value.range_id;
                                return (
                                    <Fragment key={`${value.range_id}`}> 
                                        <Box
                                            sx={{
                                                mx: 2,
                                                mb: 2,
                                                boxShadow: '0px 2px 8px rgba(0,0,0,0.15)',
                                                borderRadius: 2,
                                                background: isSelected ? 'white' : isAvailable ? 'white' : '#CFCFCF',
                                                p: 0,
                                                transition: 'box-shadow 0.2s',
                                                border: isSelected ? '2px solid green' : 'none',
                                                position: 'relative',
                                            }}
                                        >
                                            <Grid container alignItems="center" sx={{ p: 2, pb: isSelected ? 0 : 2, cursor: isAvailable ? 'pointer' : 'default', borderRadius: 2 }} onClick={() => isAvailable && handleDaySelected(value)}>
                                                <Grid size={{ xs: 8 }}>
                                                    <Typography sx={{ fontSize: '22px', fontWeight: 700, color: isAvailable ? 'green' : '#4d4d4d', display: 'flex', alignItems: 'center' }}>
                                                        {value.day && dayOfTheWeek[value.day.toUpperCase()]}
                                                        {!isAvailable && (
                                                            <LockIcon sx={{ color: 'grey', fontSize: '18px', ml: 1 }} />
                                                        )}
                                                    </Typography>
                                                    <Typography sx={{ fontSize: '15px', color: '#555', mt: 0.5 }}>{convertDateToReadableFormat(value.date)}</Typography>
                                                </Grid>
                                                <Grid size={{ xs: 4 }}>
                                                    <Typography sx={{ fontSize: '15px', fontWeight: 700, color: isAvailable ? 'green' : '#555' }}>
                                                        {isAvailable ? 'AVAILABLE' : 'FULLY BOOKED'}
                                                    </Typography>
                                                </Grid>
                                            </Grid>
                                            {isSelected && isAvailable && (
                                                <Box sx={{ p: 2, pt: 0 }}>
                                                    <Box sx={{ borderBottom: '1px solid #e0e0e0', mb: 2, mt: 1 }} />
                                                    {/* Slots */}
                                                    {value.slots && value.slots.length > 0 ? (
                                                        value.slots.map((slot) => {
                                                            const isSlotSelected = selectedSlot && selectedSlot.id === slot.id;
                                                            const isSlotAvailable = slot.remaining > 0;
                                                            return (
                                                                <Box key={slot.id} sx={{ mb: 2 }}>
                                                                    <Button
                                                                        fullWidth
                                                                        variant={isSlotSelected ? 'contained' : 'outlined'}
                                                                        sx={{
                                                                            fontSize: '2rem',
                                                                            fontWeight: 700,
                                                                            borderRadius: 2,
                                                                            border: '2px solid green',
                                                                            color: isSlotSelected ? 'white' : 'green',
                                                                            background: isSlotSelected ? 'green' : 'white',
                                                                            mb: 0.5,
                                                                            py: 1.5,
                                                                            boxShadow: isSlotSelected ? '0px 2px 8px rgba(0,128,0,0.10)' : 'none',
                                                                            '&:hover': {
                                                                                background: isSlotAvailable ? (isSlotSelected ? 'green' : '#f6fff6') : '#CFCFCF',
                                                                            },
                                                                        }}
                                                                        disabled={!isSlotAvailable}
                                                                        onClick={() => isSlotAvailable && handleSlotSelected(slot)}
                                                                    >
                                                                        {slot.slot}
                                                                    </Button>
                                                                    <Typography sx={{ textAlign: 'center', color: '#555', fontSize: '15px', mb: 1 }}>
                                                                        {slot.remaining} Consult{slot.remaining === 1 ? '' : 's'} Available
                                                                    </Typography>
                                                                </Box>
                                                            );
                                                        })
                                                    ) : (
                                                        <Typography sx={{ color: 'green', fontWeight: 'bold', textAlign: 'center', my: 2 }}>
                                                            ALL CONSULTATION APPOINTMENTS ARE FULLY BOOKED FOR {value?.day?.toUpperCase()} {convertDateToReadableFormat(value?.date)?.toUpperCase()}.
                                                        </Typography>
                                                    )}
                                                    <Box sx={{ borderBottom: '1px solid #e0e0e0', my: 2 }} />
                                                    {/* Doctor Info (optional, can be mocked as in patient/index.tsx) */}
                                                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                                                        <img src="/images/dr_hussain.jpg" alt="Dr. Hussain Anjum" style={{ width: 48, height: 48, borderRadius: '50%', marginRight: 12, border: '2px solid #e0e0e0' }} />
                                                        <Box>
                                                            <Typography sx={{ color: 'green', fontWeight: 600, fontSize: '15px', lineHeight: 1 }}>Meet Our Lead Doctor</Typography>
                                                            <Typography sx={{ fontWeight: 700, fontSize: '20px', color: '#333', lineHeight: 1.2 }}>Dr. Hussain Anjum</Typography>
                                                        </Box>
                                                    </Box>
                                                </Box>
                                            )}
                                        </Box>
                                    </Fragment>
                                );
                            }) : (
                                <Grid container sx={{ width: '100%' }} alignItems={'center'} justifyContent={'center'}>
                                    <Typography sx={{ fontSize: '12px', mt: 5 }} align="center">
                                        Our schedule is currently fully booked due to high demand, and we're working hard to open up more availability soon. <br /><br />Please check back later, or feel free to reach out if you have any questions—we'd love to assist you!
                                    </Typography>
                                </Grid>
                            )}
                        </Grid>
                        {/* Continue Button */}
                        <Box sx={{ mx: 2, mt: 4, mb: 2 }}>
                            <Button
                                fullWidth
                                sx={{
                                    background: selectedSlot ? 'green' : '#CFCFCF',
                                    color: 'white',
                                    fontWeight: 700,
                                    fontSize: '20px',
                                    borderRadius: 2,
                                    py: 1.5,
                                    boxShadow: selectedSlot ? '0px 2px 8px rgba(0,128,0,0.10)' : 'none',
                                    transition: 'background 0.2s',
                                }}
                                disabled={!selectedSlot}
                                onClick={handleSlotConfirmed}
                            >
                                Continue
                            </Button>
                        </Box>
                        {/* Info text */}
                        <Typography
                            sx={{
                                color: '#888',
                                fontSize: '14px',
                                mt: 4,
                                mb: 2,
                                px: 3,
                                textAlign: 'left',
                            }}
                        >
                            Any information you provide today is confidential and compliant with the Medical Board of Australia Good Medical Practice code, RACGP Standards of General Practice and our Medical Confidentiality Duty of Conduct for doctors in Australia, which means we protect your privacy and right to confidentiality
                        </Typography>
                        {/* Footer */}
                        <Box
                            sx={{
                                borderTop: '1px solid #444',
                                mt: 5,
                                pt: 2,
                                pb: 0,
                                ml: 3,
                                mr: 3,
                                textAlign: 'center',
                                display: { xs: 'block', md: 'none' },
                            }}
                        >
                            <Typography sx={{ fontSize: 18 }}>
                                Provided by <Box component="span" sx={{ color: '#007F00', fontWeight: 700, display: 'inline' }}>ZenithClinics</Box> Pty Ltd
                            </Typography>
                        </Box>
                    </Box>
                </>
            )}
        </>
    )
}


export default RebookNoShowPatient
