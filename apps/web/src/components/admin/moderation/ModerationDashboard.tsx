import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>,
  CardContent,
  Button,
  Alert,
  Snackbar,
  CircularProgress,
  Ta<PERSON>,
  Tab,
  Badge
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Pending as PendingIcon,
  Block as NotEligibleIcon,
  BarChart as StatsIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useNavigate } from '@tanstack/react-location';
import { useAuth } from '../../../hooks/auth-provider';
import adminRequestsService, { AdminRequestStats } from '../../../services/admin-requests.service';
import PendingRequestsTab from '../requests/PendingRequestsTab';
import ProcessedRequestsTab from '../requests/ProcessedRequestsTab';
import NonEligibleRequestsTab from '../requests/NonEligibleRequestsTab';
import RequestStatsTab from './ModerationStatsTab.tsx';
import PatientRequestHistoryTab from './PatientHistoryTab.tsx';
import QuestionnaireManagement from '../QuestionnaireManagement';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`moderation-tabpanel-${index}`}
      aria-labelledby={`moderation-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `moderation-tab-${index}`,
    'aria-controls': `moderation-tabpanel-${index}`,
  };
}

const ModerationDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user, doctor, session } = useAuth();

  const [currentTab, setCurrentTab] = useState(0);
  const [stats, setStats] = useState<AdminRequestStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'success'
  });

  // Check authentication
  useEffect(() => {
    const isAuthenticated = !!(user && session);
    const isAdmin = doctor?.role === 'admin';

    if (!isAuthenticated || !isAdmin) {
      // Redirect to login if not authenticated or not admin
      navigate({ to: '/login' });
      return;
    }
  }, [user, session, doctor, navigate]);

  // Load initial data
  useEffect(() => {
    const isAuthenticated = !!(user && session);
    const isAdmin = doctor?.role === 'admin';

    if (isAuthenticated && isAdmin) {
      loadStats();
    }
  }, [user, session, doctor]);

  const loadStats = async () => {
    try {
      setLoading(true);
      const statsData = await adminRequestsService.getRequestStats();
      setStats(statsData);
    } catch (error) {
      console.error('Error loading request stats:', error);
      showSnackbar('Failed to load request statistics', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      await loadStats();
      showSnackbar('Data refreshed successfully', 'success');
    } catch (error) {
      showSnackbar('Failed to refresh data', 'error');
    } finally {
      setRefreshing(false);
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  // Check if user is authenticated and authorized
  const isAuthenticated = !!(user && session);
  const isAdmin = doctor?.role === 'admin';

  if (!isAuthenticated || !isAdmin) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: '400px', gap: 2 }}>
        <CircularProgress />
        <Typography variant="body2" color="textSecondary">
          Checking authorization...
        </Typography>
      </Box>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Treatment Plan Requests
        </Typography>
        <Button
          variant="outlined"
          startIcon={refreshing ? <CircularProgress size={20} /> : <RefreshIcon />}
          onClick={handleRefresh}
          disabled={refreshing}
        >
          Refresh
        </Button>
      </Box>

      {/* Stats Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Pending
                    </Typography>
                    <Typography variant="h4">
                      {stats.pending}
                    </Typography>
                  </Box>
                  <PendingIcon color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Approved
                    </Typography>
                    <Typography variant="h4">
                      {stats.approved}
                    </Typography>
                  </Box>
                  <ApproveIcon sx={{ fontSize: 40, color: '#008000' }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Rejected
                    </Typography>
                    <Typography variant="h4">
                      {stats.rejected}
                    </Typography>
                  </Box>
                  <RejectIcon color="error" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Non-Eligible
                    </Typography>
                    <Typography variant="h4">
                      {stats.nonEligible}
                    </Typography>
                  </Box>
                  <NotEligibleIcon sx={{ fontSize: 40, color: '#ff5722' }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Total
                    </Typography>
                    <Typography variant="h4">
                      {stats.total}
                    </Typography>
                  </Box>
                  <StatsIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={currentTab} onChange={handleTabChange} aria-label="moderation tabs">
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <span>Pending Requests</span>
                  {stats?.pending && stats.pending > 0 && (
                    <Badge
                      badgeContent={stats.pending}
                      color="error"
                      sx={{
                        '& .MuiBadge-badge': {
                          position: 'static',
                          transform: 'none',
                          minWidth: '20px',
                          height: '20px',
                          fontSize: '0.75rem'
                        }
                      }}
                    >
                      <Box sx={{ width: 0, height: 0 }} />
                    </Badge>
                  )}
                </Box>
              }
              {...a11yProps(0)}
            />
            <Tab label="Processed Requests" {...a11yProps(1)} />
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <span>Non-Eligible Requests</span>
                  {stats?.nonEligible && stats.nonEligible > 0 && (
                    <Badge
                      badgeContent={stats.nonEligible}
                      color="error"
                      sx={{
                        '& .MuiBadge-badge': {
                          position: 'static',
                          transform: 'none',
                          minWidth: '20px',
                          height: '20px',
                          fontSize: '0.75rem'
                        }
                      }}
                    >
                      <Box sx={{ width: 0, height: 0 }} />
                    </Badge>
                  )}
                </Box>
              }
              {...a11yProps(2)}
            />
            <Tab label="Statistics" {...a11yProps(3)} />
            <Tab label="Patient History" {...a11yProps(4)} />
            <Tab label="Questionnaires" {...a11yProps(5)} />
          </Tabs>
        </Box>

        <TabPanel value={currentTab} index={0}>
          <PendingRequestsTab onStatsUpdate={loadStats} onShowSnackbar={showSnackbar} />
        </TabPanel>

        <TabPanel value={currentTab} index={1}>
          <ProcessedRequestsTab onStatsUpdate={loadStats} onShowSnackbar={showSnackbar} />
        </TabPanel>

        <TabPanel value={currentTab} index={2}>
          <NonEligibleRequestsTab onShowSnackbar={showSnackbar} />
        </TabPanel>

        <TabPanel value={currentTab} index={3}>
          <RequestStatsTab stats={stats} />
        </TabPanel>

        <TabPanel value={currentTab} index={4}>
          <PatientRequestHistoryTab onShowSnackbar={showSnackbar} />
        </TabPanel>

        <TabPanel value={currentTab} index={5}>
          <QuestionnaireManagement />
        </TabPanel>
      </Card>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ModerationDashboard;
