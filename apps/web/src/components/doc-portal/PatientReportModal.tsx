import React, { useEffect, useState } from "react";
import { Dialog, IconButton } from "@mui/material";
import { ApiClient } from "../../services";
import { ReportData } from "../../types";
import CloseIcon from "@mui/icons-material/Close";
import PatientReportTemplate from "./PatientReportTemplate";

type ReportProps = {
	id: string;
	open: boolean;
	onClose: () => void;
};

// Example data, replace with actual data fetching logic

const PatientReport: React.FC<ReportProps> = ({ id, open, onClose }) => {
	const [reportData, setReportData] = useState<ReportData>({} as ReportData);
	const [loading, setLoading] = useState(true);

	const fetchReportById = async () => {
		setLoading(true);
		try {
			// Replace with actual API call to fetch report data by ID
			const response = await ApiClient.getAICheckResponseByPatientId(id);
			if (response) {
				setReportData(response);
			}
		} catch (error) {
			console.error("Error fetching report data:", error);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (!id || id === "") {
			console.error("No report ID provided in the URL.");
			return;
		}
		fetchReportById();
	}, [id]);

	return (
		<Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
			<IconButton
				aria-label="close"
				onClick={onClose}
				sx={(theme) => ({
					position: "absolute",
					right: 8,
					top: 8,
					color: theme.palette.grey[500],
				})}
			>
				<CloseIcon />
			</IconButton>
			<PatientReportTemplate reportData={reportData} loading={loading} />
		</Dialog>
	);
};

export default PatientReport;
