import React, { useState } from "react";
import {
	<PERSON>alog,
	DialogT<PERSON>le,
	DialogContent,
	DialogActions,
	Button,
	TextField,
	Typography,
	Box,
	CircularProgress,
} from "@mui/material";

interface RejectRequestDialogProps {
	open: boolean;
	onClose: () => void;
	onConfirm: (rejectionReason: string) => Promise<void>;
	patientName: string;
	requestType: "thc_increase" | "extend_tp" | "add_22_thc" | "quantity_increase";
	loading?: boolean;
}

const RejectRequestDialog: React.FC<RejectRequestDialogProps> = ({
	open,
	onClose,
	onConfirm,
	patientName,
	requestType,
	loading = false,
}) => {
	const [rejectionReason, setRejectionReason] = useState("");
	const [error, setError] = useState("");

	const getRequestTypeDisplay = () => {
		switch (requestType) {
			case "thc_increase":
				return "THC Increase";
			case "extend_tp":
				return "Treatment Plan Extension";
			case "add_22_thc":
				return "22% THC Addition";
			case "quantity_increase":
				return "Quantity Increase";
			default:
				return "Treatment Plan Request";
		}
	};

	const handleSubmit = async () => {
		// Validation
		if (!rejectionReason.trim()) {
			setError("Please provide a reason for rejection");
			return;
		}

		if (rejectionReason.trim().length < 2) {
			setError("Rejection reason must be at least 2 characters long");
			return;
		}

		try {
			setError("");
			await onConfirm(rejectionReason.trim());
			handleClose();
		} catch (err) {
			setError("Failed to reject request. Please try again.");
		}
	};

	const handleClose = () => {
		setRejectionReason("");
		setError("");
		onClose();
	};

	const handleKeyPress = (event: React.KeyboardEvent) => {
		if (event.key === "Enter" && event.ctrlKey) {
			handleSubmit();
		}
	};

	return (
		<Dialog
			open={open}
			onClose={loading ? undefined : handleClose}
			maxWidth="sm"
			fullWidth
			sx={{
				"& .MuiDialog-paper": {
					borderRadius: 2,
					maxHeight: "80vh",
				},
			}}
		>
			<DialogTitle sx={{ pb: 1 }}>
				<Typography variant="h6" component="div" sx={{ fontWeight: "bold" }}>
					Reject {getRequestTypeDisplay()} Request
				</Typography>
				<Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
					Patient: {patientName}
				</Typography>
			</DialogTitle>

			<DialogContent sx={{ pt: 2 }}>
				<Box sx={{ mb: 2 }}>
					<Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
						Please provide a detailed reason for rejecting this request. This will be recorded for audit
						purposes.
					</Typography>
				</Box>

				<TextField
					autoFocus
					multiline
					rows={4}
					fullWidth
					label="Rejection Reason"
					placeholder="Enter the reason for rejecting this treatment plan request..."
					value={rejectionReason}
					onChange={(e) => setRejectionReason(e.target.value)}
					onKeyDown={handleKeyPress}
					error={!!error}
					helperText={error || "Minimum 2 characters required"}
					disabled={loading}
					sx={{
						"& .MuiOutlinedInput-root": {
							"& fieldset": {
								borderColor: error ? "error.main" : "grey.300",
							},
							"&:hover fieldset": {
								borderColor: error ? "error.main" : "primary.main",
							},
						},
					}}
				/>

				<Box sx={{ mt: 1, display: "flex", justifyContent: "space-between", alignItems: "center" }}>
					<Typography variant="caption" color="text.secondary">
						{rejectionReason.length}/500 characters
					</Typography>
					<Typography variant="caption" color="text.secondary">
						Ctrl + Enter to submit
					</Typography>
				</Box>
			</DialogContent>

			<DialogActions sx={{ px: 3, pb: 3, pt: 1 }}>
				<Button
					onClick={handleClose}
					disabled={loading}
					sx={{
						color: "text.secondary",
						"&:hover": {
							backgroundColor: "grey.100",
						},
					}}
				>
					Cancel
				</Button>
				<Button
					onClick={handleSubmit}
					variant="contained"
					color="error"
					disabled={loading || !rejectionReason.trim() || rejectionReason.trim().length < 2}
					sx={{
						minWidth: 120,
						fontWeight: "bold",
						"&:disabled": {
							backgroundColor: "grey.300",
							color: "grey.500",
						},
					}}
				>
					{loading ? <CircularProgress size={20} color="inherit" /> : "Reject Request"}
				</Button>
			</DialogActions>
		</Dialog>
	);
};

export default RejectRequestDialog;
