/// <reference types="vite-plugin-svgr/client" />

import React, { useEffect, useState } from "react";
import { useEmailContext } from "../../../hooks/email-provider";
import { Button, Typography, Dialog, DialogContent, DialogActions, Alert, CircularProgress, Box } from "@mui/material";
import { usePatient } from "../../../hooks/patient-provider";
import { useAuth } from "../../../hooks/auth-provider";
import { ApiClient } from "../../../services";
import { AxiosError } from "axios";
import CancelIcon from "@mui/icons-material/Cancel";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import Grid from "@mui/material/Grid2";
import { useNavigate } from "@tanstack/react-location";
import ConsultationMessage from "./intro";
import EmailDiagnosis from "./diagnosis";
import EmailTreatmentPlan from "./treatment-plan";
import EmailStrainAdvice from "./strain-advice";
import LoadingScreen from "../../../utils/loading-screen";
import { PatientTreatmentPlan } from "../../../types";
import { UserActions } from "../../../utils";
import { useTracker } from "../../../hooks/activity-tracker-provider";

// Feature flag to match doctor-consultation.tsx - easy to disable
//const ENABLE_COUNTDOWN_TIMERS = true;

const EmailPreview: React.FC = () => {
	const { introMessage, listTitle, listItemText, otherTreatment, checkedSativa, checkedIndica, checkedHybrid } =
		useEmailContext();

	const [isLoading, setIsLoading] = useState(false);
	const {
		selectedPatient,
		patientTreatmentPlan,
		updateLockStatus,
		setError,
		setPatientTreatmentPlan,
		setSelectedPlan,
		setStrength,
		setSelectedPatient,
		setReloadConsultationForm,
		reloadConsultationForm,
	} = usePatient();

	const { doctor } = useAuth();
	const [openResultDialog, setOpenResultDialog] = useState(false);
	const [openCancelConfirmDialog, setOpenCancelConfirmDialog] = useState(false);
	const [title, setTitle] = useState("");
	const [message, setMessage] = useState("");
	const [success, setSuccess] = useState(false);
	const { trackActivity } = useTracker();

	// New states for timer functionality
	const [isAutoGenerated, setIsAutoGenerated] = useState(false);
	//const [timeRemaining, setTimeRemaining] = useState(60) // 1 minute countdown
	//const timerInterval = useRef<NodeJS.Timeout | null>(null)

	const navigate = useNavigate();

	const defaultSativa = [
		"Avoid Sativa Strains Before Bedtime. These products can disturb sleep. Use calming strains instead.",
		"Limit Sativa Use for Deep Focus Tasks. Mental stimulation may hinder calm concentration.",
		"If You Have Anxiety, Avoid Sativa. These products can heighten anxiety and panic attacks.",
	];

	const defaultIndica = [
		"Avoid Indica During the Day If You Need to Be Alert. Its sedative effects can cause drowsiness.",
		"Avoid Indica When Feeling Depressed. It may increase lethargy and heaviness.",
		"Avoid Indica If Prone to Low Blood Pressure. It may cause dizziness or light-headedness.",
	];

	const defaultHybrid = [
		"Use Hybrids Carefully for Focus. Effects vary; some strains help focus, others cloud the mind.",
		"Avoid Hybrids When Quick Reflexes Are Needed. These strains may impair your response time.",
		"Do Not Rely on Hybrids for Predictable Effects. Their balance can vary and affect mood unpredictably.",
	];

	const submitTreatmentPlan = async (defaultValue: boolean = false) => {
		setIsLoading(true);
		try {
			if (!selectedPatient) {
				setIsLoading(false);
				setTitle("Select a Patient");
				setMessage(`You must select a patient`);
				setOpenResultDialog(true);
			} else if (
				checkedSativa.length <= 0 &&
				checkedIndica.length <= 0 &&
				checkedHybrid.length <= 0 &&
				!defaultValue
			) {
				setIsLoading(false);
				setTitle("Email Error");
				setMessage(`You must select at least one Strain Advice`);
				setOpenResultDialog(true);
			} else {
				if (patientTreatmentPlan) {
					const TreatmentPlanWithEmail: PatientTreatmentPlan = {
						...patientTreatmentPlan,
						drName: doctor?.username,
						drAphraNumber: doctor?.aphraNumber,
						email: {
							introMessage: introMessage,
							otherTreatment: otherTreatment,
							listTitle: listTitle,
							listItemText: listItemText,
							checkedSativa: checkedSativa.length > 0 || !defaultValue ? checkedSativa : defaultSativa,
							checkedIndica: checkedIndica.length > 0 || !defaultValue ? checkedIndica : defaultIndica,
							checkedHybrid: checkedHybrid.length > 0 || !defaultValue ? checkedHybrid : defaultHybrid,
						},
					};

					const result = await ApiClient.postTreamentPlan(TreatmentPlanWithEmail);

					// Log diagnosis submission if diagnosis exists
					if (TreatmentPlanWithEmail.diagnosis?.trim()) {
						trackActivity(
							"Doctor",
							selectedPatient.patientID || "",
							UserActions.DIAGNOSIS_SUBMITTED,
							`${doctor?.username} submitted diagnosis with treatment plan`,
							doctor?.id || "",
							selectedPatient.patientID
						).catch((error) => console.error("Failed to log diagnosis submission:", error));
					}

					await ApiClient.completedPatientWaitingQueue(selectedPatient.patientID);
					handleCompleteButton();

					setTimeout(async () => {
						const drIdLocal = localStorage.getItem("xdr");
						const doctorID =
							drIdLocal && drIdLocal !== ""
								? drIdLocal
								: doctor && doctor.accessID
									? doctor.accessID
									: "";

						const nextPatient = await ApiClient.getNextPatientAutomatically(doctorID);
						if (nextPatient?.patientID) {
							const drIdLocal = localStorage.getItem("xdr");
							const doctorID =
								drIdLocal && drIdLocal !== ""
									? drIdLocal
									: doctor && doctor.accessID
										? doctor.accessID
										: "";
							await ApiClient.alertAwayPatientOnAdmit(nextPatient);
							// setNextPatientData(nextPatient)
							updateLockStatus(nextPatient, true, doctorID);
							await ApiClient.postRedirect(nextPatient);
							await ApiClient.postPatientAdmission(nextPatient.patientID, doctorID);
							trackActivity(
								"Doctor",
								selectedPatient.patientID || "",
								UserActions.COMPLETED,
								`Patient has been sent an email from Doctor ${doctor?.username}`,
								doctor?.id || "",
								selectedPatient.patientID
							);
							setSelectedPatient(undefined);
							trackActivity(
								"Doctor",
								nextPatient.patientID || "",
								UserActions.ADMITTED,
								"Redirected to next patient from email submission",
								doctor?.id || "",
								nextPatient.patientID
							);
							localStorage.removeItem("reloaded");
							localStorage.removeItem("AUTO_GENERATED_PLAN"); // Clean up flag
							navigate({ to: "/doctor-consultation", search: { token: nextPatient.patientID } });
							setReloadConsultationForm(!reloadConsultationForm);
						} else {
							trackActivity(
								"Doctor",
								"",
								UserActions.REDIRECTED,
								`${doctor?.username} auto redirect to online patient after submitting Treatment Plan. No available patient`,
								doctor?.id || "",
								nextPatient?.patientID || ""
							);
							localStorage.removeItem("reloaded");
							localStorage.removeItem("AUTO_GENERATED_PLAN"); // Clean up flag
							navigate({ to: "/online-patients" });
						}
					}, 2000);

					if (result) {
						localStorage.removeItem("TpPlan");
						setTitle("Email Sent");
						setMessage(`You have successfully sent email to ${selectedPatient?.fullName || "- - -"}`);
						setSuccess(true);
						setStrength("22");
						setSelectedPlan("");
						setSelectedPatient(undefined);
						setPatientTreatmentPlan(undefined);
						setOpenResultDialog(true);
					}
					setIsLoading(false);
				}
			}
		} catch (error) {
			setTitle("Email Failed");
			setMessage(`We could not send the email to ${selectedPatient?.fullName || "- - -"}. Contact Admin`);
			setOpenResultDialog(true);
			setError(error as AxiosError<unknown, Error>);
		} finally {
			setIsLoading(false);
		}
	};

	// Function to show cancel confirmation dialog
	const handleCancel = () => {
		setOpenCancelConfirmDialog(true);
	};

	// Function to handle actual cancellation after confirmation
	const handleConfirmCancel = async () => {
		setIsLoading(true);
		try {
			if (patientTreatmentPlan && selectedPatient) {
				const TreatmentPlanWithEmail: PatientTreatmentPlan = {
					...patientTreatmentPlan,
					drName: doctor?.username,
					drAphraNumber: doctor?.aphraNumber,
					email: {
						introMessage: {
							intro: `[CANCELLED BY DOCTOR] ${introMessage.intro}`,
							conclusion: introMessage.conclusion,
						},
						otherTreatment: otherTreatment,
						listTitle: listTitle,
						listItemText: listItemText,
						checkedSativa: checkedSativa.length > 0 ? checkedSativa : defaultSativa,
						checkedIndica: checkedIndica.length > 0 ? checkedIndica : defaultIndica,
						checkedHybrid: checkedHybrid.length > 0 ? checkedHybrid : defaultHybrid,
					},
				};

				// Close the confirmation dialog
				setOpenCancelConfirmDialog(false);

				// This would normally call a new endpoint to save to DB only, but for now:
				// We'll use the existing endpoint and mark it as cancelled in the message
				await ApiClient.postTreamentPlan(TreatmentPlanWithEmail);

				// Log diagnosis submission if diagnosis exists (even for cancelled plans)
				if (TreatmentPlanWithEmail.diagnosis?.trim()) {
					trackActivity(
						"Doctor",
						selectedPatient.patientID || "",
						UserActions.DIAGNOSIS_SUBMITTED,
						`${doctor?.username} submitted diagnosis with cancelled treatment plan`,
						doctor?.id || "",
						selectedPatient.patientID
					).catch((error) => console.error("Failed to log diagnosis submission:", error));
				}

				// Log the cancel action
				trackActivity(
					"Doctor",
					selectedPatient.patientID || "",
					UserActions.CANCELLED,
					"Treatment plan was cancelled by doctor",
					doctor?.id || "",
					selectedPatient.patientID
				);
				await ApiClient.completedPatientWaitingQueue(selectedPatient.patientID);

				// Show success message
				setTitle("Plan Cancelled");
				setMessage(`Treatment plan was cancelled and will not be sent to the patient.`);
				setSuccess(true);
				setOpenResultDialog(true);

				// Clean up and move to next patient after a short delay
				setTimeout(async () => {
					const drIdLocal = localStorage.getItem("xdr");
					const doctorID =
						drIdLocal && drIdLocal !== "" ? drIdLocal : doctor && doctor.accessID ? doctor.accessID : "";

					const nextPatient = await ApiClient.getNextPatientAutomatically(doctorID);
					if (nextPatient?.patientID) {
						updateLockStatus(nextPatient, true, doctorID);
						await ApiClient.alertAwayPatientOnAdmit(nextPatient);
						await ApiClient.postRedirect(nextPatient);
						await ApiClient.postPatientAdmission(nextPatient.patientID, doctorID);
						trackActivity(
							"Doctor",
							nextPatient.patientID || "",
							UserActions.ADMITTED,
							"Redirected to next patient from email cancellation",
							doctor?.id || "",
							nextPatient.patientID
						);
						setSelectedPatient(undefined);
						localStorage.removeItem("reloaded");
						localStorage.removeItem("AUTO_GENERATED_PLAN"); // Clean up flag
						navigate({ to: "/doctor-consultation", search: { token: nextPatient.patientID } });
						setReloadConsultationForm(!reloadConsultationForm);
					} else {
						localStorage.removeItem("reloaded");
						localStorage.removeItem("AUTO_GENERATED_PLAN"); // Clean up flag
						navigate({ to: "/online-patients" });
					}
				}, 2000);
			}
		} catch (error) {
			setOpenCancelConfirmDialog(false);
			setTitle("Cancellation Failed");
			setMessage(`There was an error cancelling the treatment plan.`);
			setOpenResultDialog(true);
			setError(error as AxiosError<unknown, Error>);
		} finally {
			setIsLoading(false);
		}
	};

	const handleDialogClose = () => {
		setOpenResultDialog(false);
	};

	const handleCompleteButton = async () => {
		setPatientTreatmentPlan(undefined);
		setStrength("22");
		setSelectedPlan("");
		setOpenResultDialog(false);
	};

	// Start the countdown timer
	// const startTimer = () => {
	//     if (!ENABLE_COUNTDOWN_TIMERS) return;

	//     setTimeRemaining(60); // 1 minute

	//     timerInterval.current = setInterval(() => {
	//         setTimeRemaining(prev => {
	//             if (prev <= 1) {
	//                 clearInterval(timerInterval.current!);
	//                 return 0;
	//             }
	//             return prev - 1;
	//         });
	//     }, 1000);
	// };

	useEffect(() => {
		if (!selectedPatient && !patientTreatmentPlan) {
			const savedPlan = localStorage.getItem("TpPlan");
			if (savedPlan) {
				const savedTreatmentPlan = JSON.parse(savedPlan) as PatientTreatmentPlan;
				setPatientTreatmentPlan(savedTreatmentPlan);
				setSelectedPatient(savedTreatmentPlan.patient);
			}
		}

		// Check if this is an auto-generated plan
		const autoGenFlag = localStorage.getItem("AUTO_GENERATED_PLAN");
		if (autoGenFlag === "true") {
			setIsAutoGenerated(true);
			trackActivity(
				"Doctor",
				selectedPatient?.patientID || "",
				UserActions.AUTO_PLAN,
				"Auto-generated treatment plan loaded",
				doctor?.id || "",
				selectedPatient?.patientID || ""
			);
		}

		// Start countdown timer
		// if (ENABLE_COUNTDOWN_TIMERS) {
		//     startTimer();
		// }

		const submitTimeOut = setTimeout(
			async () => {
				await submitTreatmentPlan(true);
				trackActivity(
					"Doctor",
					selectedPatient?.patientID || "",
					UserActions.COMPLETED,
					"Submitting Default Strains",
					doctor?.id || "",
					selectedPatient?.patientID || ""
				);
			},
			1 * 60 * 1000
		);

		return () => {
			clearTimeout(submitTimeOut);
			// if (timerInterval.current) {
			//     clearInterval(timerInterval.current);
			// }
		};
	}, []);

	useEffect(() => {
		const handleVisibilityChange = async () => {
			if (document.visibilityState === "visible") {
				trackActivity(
					"Doctor",
					"",
					UserActions.RETURNED,
					`${doctor?.username} returned to email screen`,
					doctor?.id || "",
					selectedPatient?.patientID || ""
				);
			} else if (document.visibilityState === "hidden") {
				trackActivity(
					"Doctor",
					"",
					UserActions.AWAY,
					`${doctor?.username} left email screen`,
					doctor?.id || "",
					selectedPatient?.patientID || ""
				);
			}
		};

		document.addEventListener("visibilitychange", handleVisibilityChange);

		return () => {
			document.removeEventListener("visibilitychange", handleVisibilityChange);
		};
	}, []);

	return (
		<>
			{isLoading && <LoadingScreen />}

			{/* Result Dialog */}
			<Dialog open={openResultDialog} fullWidth={true} maxWidth={"xs"} onClose={handleDialogClose}>
				<DialogContent>
					<Grid
						container
						direction={"column"}
						sx={{ width: "100%" }}
						justifyContent={"center"}
						alignItems={"center"}
					>
						<Typography sx={{ fontSize: "18px", fontWeight: "bold", mb: 3 }}>{title}</Typography>
						{success ? (
							<CheckCircleIcon sx={{ width: "100px", height: "100px", color: "green", mb: 2 }} />
						) : (
							<CancelIcon sx={{ width: "100px", height: "100px", color: "red", mb: 2 }} />
						)}
						<Typography sx={{ fontSize: "14px" }} align="center">
							{message}
						</Typography>
					</Grid>
				</DialogContent>
			</Dialog>

			{/* Cancel Confirmation Dialog */}
			<Dialog
				open={openCancelConfirmDialog}
				onClose={() => !isLoading && setOpenCancelConfirmDialog(false)}
				maxWidth="sm"
				fullWidth
			>
				<DialogContent>
					<Box sx={{ textAlign: "center", mb: 2 }}>
						<Typography variant="h6" sx={{ mb: 2 }}>
							Cancel Treatment Plan
						</Typography>
						<Typography>Are you sure you want to cancel this treatment plan?.</Typography>
					</Box>
				</DialogContent>
				<DialogActions>
					<Button
						onClick={() => setOpenCancelConfirmDialog(false)}
						disabled={isLoading}
						sx={{
							color: "#676767",
							fontSize: "14px",
							textTransform: "none",
						}}
					>
						No, Keep Editing
					</Button>
					<Button
						onClick={handleConfirmCancel}
						variant="contained"
						sx={{
							bgcolor: "grey.500",
							"&:hover": {
								bgcolor: "grey.700",
							},
							fontSize: "14px",
							textTransform: "none",
						}}
						disabled={isLoading}
						startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : null}
					>
						{isLoading ? "Processing..." : "Yes, Cancel Plan"}
					</Button>
				</DialogActions>
			</Dialog>

			<Grid
				display="inline-flex"
				alignItems="start"
				sx={{ width: "80%" }}
				direction={"column"}
				container
				className="email"
			>
				<Grid container sx={{ mb: 3 }}>
					<Typography sx={{ fontSize: "24px", fontWeight: "bold", color: "green" }}>
						Review Patient Email
					</Typography>

					{/* Countdown Timer */}
					{/* {ENABLE_COUNTDOWN_TIMERS && (
                        <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            ml: 'auto',
                            p: 1,
                            borderRadius: 1,
                            bgcolor: timeRemaining <= 10 ? 'error.light' : timeRemaining <= 30 ? 'warning.light' : 'info.light'
                        }}>
                            <TimerIcon sx={{ mr: 1, color: timeRemaining <= 10 ? 'error.main' : timeRemaining <= 30 ? 'warning.main' : 'info.main' }} />
                            <Typography sx={{
                                fontWeight: 'bold',
                                color: timeRemaining <= 10 ? 'error.main' : timeRemaining <= 30 ? 'warning.main' : 'info.main'
                            }}>
                                {Math.floor(timeRemaining / 60)}:{(timeRemaining % 60).toString().padStart(2, '0')}
                            </Typography>
                        </Box>
                    )} */}
				</Grid>

				{/* Auto-generated plan notification */}
				{isAutoGenerated && (
					<Alert severity="info" sx={{ width: "100%", mb: 2 }}>
						Thank you for your consultation. We hope your session went well. A treatment plan has been
						automatically submitted for you. Kindly review the details
					</Alert>
				)}

				<ConsultationMessage />
				<EmailDiagnosis />
				<EmailTreatmentPlan />
				<EmailStrainAdvice />
				{/*
                <Grid sx={{ width: '100%', mt: 2 }} container justifyContent={'center'}>
                    <Button sx={{ textTransform: 'none', backgroundColor: 'green', width: '50%' }} variant="contained" onClick={() => submitTreatmentPlan()}>
                        Send Email to {selectedPatient?.fullName || '- - -'}
                    </Button> */}

				<Grid sx={{ width: "100%", mt: 2 }} container justifyContent={"center"} spacing={2}>
					<Grid>
						<Button
							sx={{ textTransform: "none", backgroundColor: "green" }}
							variant="contained"
							onClick={() => submitTreatmentPlan()}
						>
							Confirm & Send Email to {selectedPatient?.fullName || "- - -"}
						</Button>
					</Grid>
					{isAutoGenerated && (
						<Grid>
							<Button
								sx={{ textTransform: "none", borderColor: "grey.500", color: "grey.500" }}
								variant="outlined"
								onClick={handleCancel}
							>
								Cancel
							</Button>
						</Grid>
					)}
				</Grid>
			</Grid>
		</>
	);
};

export default EmailPreview;
