import React, { useState } from "react";
import { <PERSON>, Typo<PERSON>, IconButton, Collapse } from "@mui/material";
import Grid from "@mui/material/Grid2";
//import { useTheme } from "@mui/material/styles";
import { usePatient } from "../../../hooks/patient-provider";
import { UserActions } from "../../../utils";
import { useAuth } from "../../../hooks/auth-provider";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { useTracker } from "../../../hooks/activity-tracker-provider";

const EmailDiagnosis: React.FC = () => {
	const { patientTreatmentPlan, selectedPatient } = usePatient();
	const { doctor } = useAuth();
	const { trackActivity } = useTracker();
	// const theme = useTheme();
	//const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
	const [open, setOpen] = useState(false);

	const handleToggle = () => {
		setOpen(!open);

		// Log diagnosis view when expanded
		if (!open && selectedPatient) {
			const comment = `Doctor ${doctor?.username} viewed diagnosis in email confirmation`;
			trackActivity(
				"Doctor",
				selectedPatient.patientID || "",
				UserActions.DIAGNOSIS_VIEWED,
				comment,
				doctor?.id || "",
				selectedPatient.patientID
			).catch((error) => console.error("Failed to log diagnosis view:", error));
		}
	};

	// Don't render if no diagnosis
	if (!patientTreatmentPlan?.diagnosis?.trim()) {
		return null;
	}

	return (
		<Grid container sx={{ width: "100%", fontSize: "12px" }} direction={"column"}>
			<Grid container alignItems={"center"}>
				<IconButton
					onClick={handleToggle}
					sx={{
						transform: open ? "rotate(90deg)" : "rotate(0deg)",
						width: "20px",
						height: "20px",
						mr: 2,
						transition: "transform 0.3s",
					}}
				>
					<ArrowRightIcon sx={{ color: "black" }} />
				</IconButton>
				<span style={{ fontWeight: "bold", fontSize: "24px" }}>Your Diagnosis</span>
			</Grid>

			<Collapse in={open} timeout={10}>
				<Grid sx={{ width: "100%", fontSize: "14px", mt: 2 }}>
					<Typography sx={{ mb: 2, fontWeight: "medium" }}>
						Based on your consultation, your doctor has provided the following diagnosis:
					</Typography>

					<Box
						sx={{
							p: 2,
							backgroundColor: "#f8f9fa",
							borderRadius: "8px",
							border: "1px solid #e9ecef",
						}}
					>
						<Typography sx={{ fontSize: "14px", lineHeight: 1.6 }}>
							{patientTreatmentPlan.diagnosis}
						</Typography>
					</Box>

					<Typography sx={{ mt: 2, fontSize: "12px", color: "#6c757d" }}>
						This diagnosis helps guide your treatment plan and ensures you receive the most appropriate care
						for your condition. If you have any questions about your diagnosis, please don't hesitate to
						contact your healthcare provider.
					</Typography>
				</Grid>
			</Collapse>
		</Grid>
	);
};

export default EmailDiagnosis;
