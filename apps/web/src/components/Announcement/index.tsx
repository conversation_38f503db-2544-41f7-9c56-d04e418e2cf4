import { Button, <PERSON>alog, DialogTitle, TextField, <PERSON>l<PERSON>, Typography, IconButton } from '@mui/material';
import Grid from '@mui/material/Grid2';
import AddIcon from '@mui/icons-material/Add';
import { useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { AnnouncementData } from '../../types';
import { ApiClient } from '../../services';
import { useSnackbar } from 'notistack';
import { useAuth } from '../../hooks/auth-provider';

const Announcement: React.FC = () => {
    const [openForm, setOpenForm] = useState(false)
    const [openDelete, setOpenDelete] = useState(false)
    const { enqueueSnackbar } = useSnackbar();
    const { announcements, setAnnouncement } = useAuth()
    const [deleteId, settDeleteId] = useState('')

    const [data, setData] = useState<AnnouncementData>({
        id: '',
        title: '',
        announcement: ''
    })

    const handleDeleteAlert = async (event: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: string) => {
        event.stopPropagation();
        setOpenDelete(true)
        settDeleteId(id)
    };

    const handleDelete = async () => {
        setOpenDelete(false)
        if (deleteId.length > 0) {
            setAnnouncement(() => {
                const filter = announcements.filter((a) => a.id !== deleteId)
                return filter
            })
            settDeleteId('')
            await ApiClient.deleteAnnouncement(deleteId).catch((e) => {
                enqueueSnackbar(e.message, {
                    variant: 'error'
                })
            })
        }
    };

    const handleFormClose = () => {
        setData({
            id: '',
            title: '',
            announcement: ''
        })
        setOpenForm(false)
    }

    const handleFormChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const name = event.target.name

        setData((prev) => {
            return {
                ...prev,
                [name]: event.target.value
            }
        })
    }

    const handleSubmitForm = async () => {
        setOpenForm(false)
        setAnnouncement((prev) => {
            const copy = [...prev]
            copy.unshift(data)
            return copy
        })
        await ApiClient.updateAnnouncement(data).catch((e) => {
            enqueueSnackbar(e.message, {
                variant: 'error'
            })
        })
    }

    return (
        <>

            <Dialog open={openForm} onClose={handleFormClose} maxWidth='md' fullWidth={true}>
                <DialogTitle>
                    <Grid container>
                        <Grid>
                            <Typography sx={{ fontSize: '28px' }}>
                                Announcement
                            </Typography>
                        </Grid>
                        <Toolbar sx={{ flexGrow: 1, m: -5 }} />
                        <Grid>
                            <IconButton onClick={handleFormClose}>
                                <CloseIcon />
                            </IconButton>
                        </Grid>
                    </Grid>
                </DialogTitle>
                <Grid container sx={{ p: 2 }} >
                    <Grid container direction='column' sx={{ width: '100%', mb: 5 }}>
                        <Grid>
                            <Typography>
                                Tile
                            </Typography>
                        </Grid>
                        <TextField name='title' value={data.title} onChange={handleFormChange} />
                    </Grid>

                    <Grid container direction='column' sx={{ width: '100%' }}>
                        <Grid>
                            <Typography>
                                Announcement
                            </Typography>
                        </Grid>
                        <TextField multiline rows={15} name='announcement' value={data.announcement} onChange={handleFormChange} />
                    </Grid>

                    <Grid container sx={{ mt: 2 }}>
                        <Grid>
                            <Button variant='contained' onClick={handleSubmitForm}>
                                Confirm
                            </Button>
                        </Grid>
                    </Grid>

                </Grid>
            </Dialog>
            <Dialog open={openDelete} onClose={() => setOpenDelete(false)} maxWidth='md'>
                <DialogTitle>
                    You are about to delete an announcement
                </DialogTitle>

                <Grid container spacing={2} sx={{ width: '100%', p: 2 }} alignItems='center' justifyContent={'center'}>
                    <Grid>
                        <Button variant='contained' sx={{ backgroundColor: 'red' }} onClick={handleDelete}>
                            Confirm
                        </Button>
                    </Grid>
                    <Grid>
                        <Button onClick={() => setOpenDelete(false)}>
                            Cancel
                        </Button>
                    </Grid>
                </Grid>
            </Dialog>
            <Grid container sx={{ width: '100%' }}>
                <Grid container sx={{ width: '100%' }} justifyContent='end'>
                    <Grid>
                        <Button variant='outlined' endIcon={<AddIcon />} onClick={() => setOpenForm(true)}>
                            New Announcement
                        </Button>
                    </Grid>
                </Grid>
                <Toolbar sx={{ m: -2 }} />

                {announcements.map((item, index) => {
                    return (
                        <div key={item.title + '' + index} style={{ width: '100%', marginTop: '16px' }} onClick={() => {
                            setData(item)
                            setOpenForm(true)
                        }}>
                            <Grid
                                container
                                sx={{
                                    width: '100%',
                                    cursor: 'pointer',
                                    border: '1px solid #E0E0E0',
                                    backgroundColor: '#F5F5F5',
                                    borderRadius: '8px',
                                    padding: '16px',
                                    transition: 'all 0.3s ease',
                                    '&:hover': {
                                        backgroundColor: '#E8ECEF',
                                        borderColor: '#B0B0B0',
                                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                    },
                                }}
                            >
                                <Grid container direction="column" spacing={1}>
                                    <Grid>
                                        <Typography variant="h6" sx={{ color: '#333' }}>
                                            {item.title}
                                        </Typography>
                                    </Grid>
                                    <Grid>
                                        <Typography variant="body1" sx={{ color: '#555' }}>
                                            {item.announcement.length > 50 ? `${item.announcement.slice(0, 50)}...` : item.announcement}
                                        </Typography>
                                    </Grid>
                                </Grid>
                                <Toolbar sx={{ flexGrow: 1 }} />
                                <Grid>
                                    <Button
                                        variant="contained"
                                        onClick={(e) => handleDeleteAlert(e, item.id)}
                                        sx={{
                                            backgroundColor: '#D32F2F',
                                            color: '#FFFFFF',
                                            '&:hover': {
                                                backgroundColor: '#B71C1C',
                                            },
                                        }}
                                    >
                                        Delete
                                    </Button>
                                </Grid>
                            </Grid>
                        </div>
                    )
                })}

            </Grid>
        </>
    )
}

export default Announcement