import React, { useContext, useEffect, useState } from "react";
import { PatientQueue, TimeLeft } from "../types";
import { PatientData } from "../types"
import { ApiClient } from "../services";

interface MeetingProviderProps {
    timeLeft: TimeLeft | undefined
    setTimeLeft: React.Dispatch<React.SetStateAction<TimeLeft | undefined>>
    stopTimer: boolean
    setStopTimer: React.Dispatch<React.SetStateAction<boolean>>
    admittedPatient: PatientData | undefined
    setAdmittedPatient: React.Dispatch<React.SetStateAction<PatientData | undefined>>
    patientQueue: PatientQueue[]
    handlePatientQueue: (action: "add" | "update" | "remove", patient: PatientQueue) => PatientQueue | undefined
    setPatientQueue: React.Dispatch<React.SetStateAction<PatientQueue[]>>
}

type WebSocketMessage = {
    type: string
    data: unknown
}

const MeetingContext = React.createContext<MeetingProviderProps | null>(null)

export const MeetingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {

    const [timeLeft, setTimeLeft] = useState<TimeLeft | undefined>(undefined)
    const [stopTimer, setStopTimer] = useState<boolean>(false)
    const [admittedPatient, setAdmittedPatient] = useState<PatientData | undefined>(undefined)
    const [patientQueue, setPatientQueue] = useState<PatientQueue[]>([])
    const [restart, setRestart] = useState(true)

    const handlePatientQueue = (action: 'add' | 'update' | 'remove', patient: PatientQueue) => {
        if (action === 'update') {
            setPatientQueue((prev) => {
                return prev.map((q) => {
                    if (q.patientID === patient?.patientID) {
                        return patient
                    }
                    return q
                })
            })
            return patient
        }

        if (action === 'remove') {
            setPatientQueue((prev) => {
                const filtered = prev.filter(p => p.patientID !== patient?.patientID)
                return filtered
            })
            return patient
        }

        if (action === 'add') {
            setPatientQueue((prev) => {
                if (!prev.find(q => q.patientID === patient?.patientID)) {
                    return [patient, ...prev]
                }
                return [...prev]
            })
        }
    }

    useEffect(() => {
        const ws = new WebSocket(`${import.meta.env.VITE_WSS}`)
        ws.onmessage = (event) => {
            const message = JSON.parse(event.data) as WebSocketMessage

            if (message.type === 'ping') {
                ws.send(JSON.stringify({ type: 'pong' }))
                return;
            }

            if (message.type === 'admitted_patient') {
                const patient = message.data as PatientData
                setAdmittedPatient(patient)
                return;
            }

            if (message.type === 'update_patient_queue') {
                const queue = message.data as PatientQueue
                handlePatientQueue('update', queue)
                return;
            }

            if (message.type === 'add_patient_queue') {
                const queue = message.data as PatientQueue
                handlePatientQueue('add', queue)
                return;
            }

            if (message.type === 'remove_from_queue') {
                const queue = message.data as PatientQueue
                handlePatientQueue('remove', queue)
                return;
            }
            if (message.type === 'empty_queue') {
                setPatientQueue([])
                return;
            }

            if (message.type === 'patient_reassigned') {
                const reassignmentData = message.data as { patientID: string, newDoctorID: string }
                const { patientID, newDoctorID } = reassignmentData

                // Update the patient queue to reflect the reassignment
                setPatientQueue((prev) => {
                    return prev.map((q) => {
                        if (q.patientID === patientID) {
                            return {
                                ...q,
                                assignedDoctorID: newDoctorID
                            }
                        }
                        return q
                    })
                })

                // Show notification to the doctor
                const doctorID = localStorage.getItem('xdr')
                if (doctorID === newDoctorID) {
                    // This doctor is receiving a reassigned patient
                    //console.log(`Patient ${patientID} has been reassigned to you`)
                    // Could show a toast notification here
                }

                return;
            }
        }

        ws.onclose = () => {
            setRestart(!restart)
        };

        ws.onerror = (error) => {
            console.log('ws error:', error);
        };

    }, [restart])


    useEffect(() => {
        // fetch the queue.
        // When Doc connect, they should fetch the current queue.
        const init = async () => {
            const queueResponse = await ApiClient.fetchQueue()
            setPatientQueue(() => {
                return [...queueResponse.patients]
            })
        }
        init()
    }, [])
    const context: MeetingProviderProps = {
        timeLeft,
        setTimeLeft,
        stopTimer,
        setStopTimer,
        admittedPatient,
        setAdmittedPatient,
        patientQueue,
        setPatientQueue,
        handlePatientQueue
    }

    return <MeetingContext.Provider value={context}>
        {children}
    </MeetingContext.Provider>

}

export const useMeeting = () => {
    const context = useContext(MeetingContext)
    if (!context) {
        throw new Error("useMeeting must tbe used within a MeetingContext")

    }

    return context
}