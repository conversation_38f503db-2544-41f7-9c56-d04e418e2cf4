import React, { createContext, useContext, useEffect, useState } from "react"
import { PatientData, PatientTreatmentPlan, Questionnaire, OrderHistory, TreatmentPlanHistory, PatientConsultation, TimeStarter, Dr, <PERSON><PERSON><PERSON><PERSON><PERSON>, Questionnaire<PERSON>hape, HealthCheckShape } from "../types"
import { ApiClient } from "../services"
import { AxiosError } from "axios"
import ApiErrorMessage from "../components/error/apiErrorMessage"
import Grid from "@mui/material/Grid2";
import { useAuth } from "./auth-provider"

type WebSocketMessage = {
    type: string
    data: unknown
}

type SupplyDocument = {
    title: string
    name: string;
}
interface PatientContextType {
    patients?: PatientData[]
    setPatient: (value: React.SetStateAction<PatientData[] | [] | undefined>) => void
    selectedPatient?: PatientData
    error?: Error | null,
    setSelectedPatient: React.Dispatch<React.SetStateAction<PatientData | undefined>>
    patientTreatmentPlan: PatientTreatmentPlan | undefined
    setPatientTreatmentPlan: React.Dispatch<React.SetStateAction<PatientTreatmentPlan | undefined>>
    getPatientById: (id: string) => PatientData | undefined
    setError: React.Dispatch<React.SetStateAction<Error | AxiosError<unknown, Error> | null>>
    selectedPlan: string
    setSelectedPlan: React.Dispatch<React.SetStateAction<string>>
    strength: "22" | "29"
    setStrength: React.Dispatch<React.SetStateAction<"22" | "29">>
    selectedForms: ("22" | "29")[]
    setSelectedForms: React.Dispatch<React.SetStateAction<("22" | "29")[]>>
    selectedHistory: Questionnaire | OrderHistory | TreatmentPlanHistory | HealthCheckShape | undefined
    setSelectedHistory: React.Dispatch<React.SetStateAction<Questionnaire | OrderHistory | TreatmentPlanHistory | HealthCheckShape | undefined>>
    waitingTimes: {
        [key: string]: number;
    }
    setWaitingTimes: React.Dispatch<React.SetStateAction<{
        [key: string]: number;
    }>>
    drId: string
    setDrId: React.Dispatch<React.SetStateAction<string>>
    updateLockStatus: (patient: PatientData, status: boolean, dr: string) => void
    notificationInterval: NodeJS.Timeout | undefined
    setNotificationInterval: React.Dispatch<React.SetStateAction<NodeJS.Timeout | undefined>>
    searchedpatients: PatientData[] | null
    setSearchedPatients: React.Dispatch<React.SetStateAction<PatientData[] | null>>
    selectedSearchedPatient: PatientData | undefined
    setSelectedSearchedPatient: React.Dispatch<React.SetStateAction<PatientData | undefined>>
    restartTimerDialog: boolean
    setRestartTimerDialog: React.Dispatch<React.SetStateAction<boolean>>
    timerKey: {
        [key: string]: string | undefined;
    }
    setTimerKey: React.Dispatch<React.SetStateAction<{
        [key: string]: string | undefined;
    }>>
    drList: {
        [key: string]: Dr | undefined;
    }
    allDoctors: Dr[]
    setAllDoctors: React.Dispatch<React.SetStateAction<Dr[]>>
    setDrList: React.Dispatch<React.SetStateAction<{
        [key: string]: Dr | undefined;
    }>>
    selectedInboxItem: TreatmentPlanHistory | null
    setSelectedInboxItem: React.Dispatch<React.SetStateAction<TreatmentPlanHistory | null>>
    inboxData: (TreatmentPlanHistory | OrderHistory | Questionnaire)[]
    setInboxData: React.Dispatch<React.SetStateAction<(TreatmentPlanHistory | OrderHistory | Questionnaire)[]>>
    currentSearchPage: number
    setCurrentSearchPage: React.Dispatch<React.SetStateAction<number>>
    updateInboxData: (data: (TreatmentPlanHistory | OrderHistory | Questionnaire)[]) => void
    fetchHistory: (patient: PatientData) => Promise<void>
    selectedPatientHistory: TreatmentPlanHistory | OrderHistory | QuestionnaireShape | PatientOrder | HealthCheckShape | undefined
    setSelectedPatientHistory: React.Dispatch<React.SetStateAction<TreatmentPlanHistory | QuestionnaireShape | PatientOrder | HealthCheckShape | undefined>>
    patientHistory: (TreatmentPlanHistory | QuestionnaireShape | PatientOrder | HealthCheckShape)[]
    setPatientHistory: React.Dispatch<React.SetStateAction<(QuestionnaireShape | TreatmentPlanHistory | PatientOrder | HealthCheckShape)[]>>
    selectedSupplyDocument: SupplyDocument | undefined
    setSelectedSupplyDocument: React.Dispatch<React.SetStateAction<SupplyDocument | undefined>>
    reloadConsultationForm: boolean
    setReloadConsultationForm: React.Dispatch<React.SetStateAction<boolean>>
    // Patient History Dialog State
    isPatientHistoryDialogOpen: boolean
    isPatientHistoryLoading: boolean
    patientHistoryError: string | null
    openPatientHistoryDialog: (patientId?: string, patientEmail?: string) => Promise<void>
    closePatientHistoryDialog: () => void
}

const PatientContext = createContext<PatientContextType | null>(null)

export const PatientProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [patients, setPatient] = useState<PatientData[] | []>()
    const [selectedPatient, setSelectedPatient] = useState<PatientData | undefined>(undefined)
    const [error, setError] = useState<AxiosError<unknown, Error> | Error | null>(null)
    const [patientTreatmentPlan, setPatientTreatmentPlan] = useState<PatientTreatmentPlan | undefined>(undefined)
    const [selectedPlan, setSelectedPlan] = useState('')
    const [strength, setStrength] = useState<'22' | '29'>('22')
    const [selectedForms, setSelectedForms] = useState<('22' | '29')[]>([])
    const [selectedHistory, setSelectedHistory] = useState<Questionnaire | OrderHistory | TreatmentPlanHistory | HealthCheckShape | undefined>(undefined)
    const [waitingTimes, setWaitingTimes] = useState<{ [key: string]: number }>({});
    const [notificationInterval, setNotificationInterval] = useState<NodeJS.Timeout>()
    const [searchedpatients, setSearchedPatients] = useState<PatientData[] | null>(null)
    const [selectedSearchedPatient, setSelectedSearchedPatient] = useState<PatientData>()
    const [restartTimerDialog, setRestartTimerDialog] = useState(false)
    const [drId, setDrId] = useState('')
    const { user, doctor } = useAuth()
    const [restart, setRestart] = useState(true)
    const [timerKey, setTimerKey] = useState<{ [key: string]: string | undefined }>({})
    const [drList, setDrList] = useState<{ [key: string]: Dr | undefined }>({})
    const [allDoctors, setAllDoctors] = useState<Dr[]>([])
    const [selectedInboxItem, setSelectedInboxItem] = useState<TreatmentPlanHistory | null>(null)
    const [inboxData, setInboxData] = useState<(TreatmentPlanHistory | OrderHistory | Questionnaire)[]>([])
    const [currentSearchPage, setCurrentSearchPage] = useState(1)
    const [selectedPatientHistory, setSelectedPatientHistory] = useState<QuestionnaireShape | TreatmentPlanHistory | PatientOrder | HealthCheckShape | undefined>(undefined)
    const [patientHistory, setPatientHistory] = useState<(QuestionnaireShape | TreatmentPlanHistory | PatientOrder | HealthCheckShape)[]>([])
    const [selectedSupplyDocument, setSelectedSupplyDocument] = useState<SupplyDocument | undefined>(undefined)
    const [reloadConsultationForm, setReloadConsultationForm] = useState(false)
    // Patient History Dialog State
    const [isPatientHistoryDialogOpen, setIsPatientHistoryDialogOpen] = useState(false)
    const [isPatientHistoryLoading, setIsPatientHistoryLoading] = useState(false)
    const [patientHistoryError, setPatientHistoryError] = useState<string | null>(null)

    // APPLY PAGINATION IN CONTEXT HERE

    const getPatientById = (id: string) => {
        const p = patients?.find(p => p.patientID === `${id}`)
        return p
    }

    const fetchHistory = async (patient: PatientData) => {
        if (patient.email) {
            const history = await Promise.all(
                [
                    ApiClient.fetchTreatmentPlanByEmail(patient.email),
                    ApiClient.fetchQuestionnaireByEmail(patient.patientID),
                    ApiClient.fetchPatientOrdersByEmail(patient.email),
                    ApiClient.fetchHealthCheckByEmail(patient.patientID)
                ]
            )

            setPatientHistory(() => {
                const data = history.flat()
                const sortedData = data.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
                return sortedData
            })
        }
    }

    const updateLockStatus = (patient: PatientData, status: boolean, dr: string) => {
        // Update the patient's assignedDoctorID in the PatientQueue table
        ApiClient.updatePatientLockedStatus(patient.patientID, status, dr)
            .then((updatedPatient) => {
                // Update the patient object with the assignedDoctorID
                if (updatedPatient && updatedPatient.assignedDoctorID) {
                    patient.assignedDoctorID = updatedPatient.assignedDoctorID;
                } else {
                    patient.assignedDoctorID = undefined;
                }
            })
            .catch((error) => {
                setError(error as AxiosError<unknown, Error>)
            })
    }

    const updateInboxData = (data: (TreatmentPlanHistory | OrderHistory | Questionnaire)[]) => {
        const sortedData = data.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        setInboxData(sortedData)
    }

    // Patient History Dialog handlers
    const openPatientHistoryDialog = async (patientId?: string, patientEmail?: string) => {
        try {
            setIsPatientHistoryDialogOpen(true)
            setPatientHistoryError(null)

            // If patientId is provided, try to get patient data and fetch history
            if (patientId) {
                setIsPatientHistoryLoading(true)

                // First try to find patient in existing patients list
                let patient = getPatientById(patientId)

                // If not found in existing list, try to fetch from API
                if (!patient) {
                    try {
                        // Try to get patient data from the patients list or API
                        const allPatients = await ApiClient.getPatientsRedis()
                        patient = allPatients?.find(p => p.patientID === patientId)
                    } catch (error) {
                        console.warn('Could not fetch patients list:', error)
                    }
                }

                // Determine which email to use for fetching history
                const emailToUse = patientEmail || patient?.email

                // If we have patient data with email, fetch their history using the proper API methods
                if (patient && emailToUse) {
                    try {
                        const history = await Promise.all([
                            ApiClient.fetchTreatmentPlanByEmail(emailToUse), // Use email for treatment plans
                            ApiClient.fetchQuestionnaireByEmail(patientId), // Use patientID for questionnaires
                            ApiClient.fetchHealthCheckByEmail(patientId), // Use patientID for health checks
                            ApiClient.fetchPatientOrdersByEmail(emailToUse) // Use email for patient orders
                        ])

                        setPatientHistory(() => {
                            const data = history.flat().filter(item => item !== null && item !== undefined)
                            const sortedData = data.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
                            return sortedData
                        })
                    } catch (error) {
                        console.error('Error fetching patient history with email:', error)
                        setPatientHistoryError('Unable to load patient history. Please try again.')
                    }
                } else {
                    // Fallback: try to fetch by patientId directly (may have limited results)
                    try {
                        const history = await Promise.all([
                            ApiClient.fetchTreatmentPlanByPatientId(patientId), // This should work with patientId
                            ApiClient.fetchQuestionnaireByEmail(patientId), // This might work with patientId
                            ApiClient.fetchHealthCheckByEmail(patientId), // This might work with patientId
                            // Skip patient orders if no email available as it requires email
                        ])

                        setPatientHistory(() => {
                            const data = history.flat().filter(item => item !== null && item !== undefined)
                            const sortedData = data.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
                            return sortedData
                        })

                        // Show warning if no email was available
                        if (!emailToUse) {
                            setPatientHistoryError('Limited history available - patient email not found. Some records may be missing.')
                        }
                    } catch (error) {
                        console.error('Error fetching patient history by ID:', error)
                        setPatientHistoryError('Unable to load patient history. Patient data may be incomplete.')
                    }
                }
            }
        } catch (error) {
            console.error('Error opening patient history dialog:', error)
            setPatientHistoryError('Failed to load patient history.')
        } finally {
            setIsPatientHistoryLoading(false)
        }
    }

    const closePatientHistoryDialog = () => {
        setIsPatientHistoryDialogOpen(false)
        setPatientHistoryError(null)
        // Optionally clear selected history when closing
        setSelectedPatientHistory(undefined)
    }


    useEffect(() => {
        const fetchPatient = async () => {
            try {
                if (user) {
                    const patients = await ApiClient.getPatientsRedis()
                    setPatient(() => {
                        return patients?.sort((a, b) => {
                            if (a.consultation?.meetingOngoing !== b.consultation?.meetingOngoing) {
                                return a.consultation?.meetingOngoing ? -1 : 1;
                            }

                            if (a.consultation?.consultationDate && b.consultation?.consultationDate) {
                                return new Date(a.consultation.consultationDate).getTime() - new Date(b.consultation.consultationDate).getTime();
                            }
                            return 0;
                        });
                    })
                    const doctors = await ApiClient.getDoctors()
                    setAllDoctors(() => {
                        return [...doctors]
                    })
                }
            } catch (error) {
                setError(error as AxiosError<unknown, Error>)
            }
        }
        if (!patients) {
            fetchPatient()
        }
    }, [])

    useEffect(() => {
        const ws = new WebSocket(`${import.meta.env.VITE_WSS}`)
        ws.onmessage = (event) => {
            const message = JSON.parse(event.data) as WebSocketMessage

            if (message.type === 'ping') {
                ws.send(JSON.stringify({ type: 'pong' }))
                return;
            }

            if (message.type === 'updated_patient') {
                const patient = message.data as PatientData
                setPatient((prev) => {
                    return prev?.map(p => {
                        if (p.patientID === patient.patientID) {
                            return patient
                        }
                        return p
                    })
                })
                return;
            }

            if (message.type === 'update_lock_status') {
                const patient = message.data as PatientData
                setPatient((prev) => {
                    return prev?.map(p => {
                        if (p.patientID === patient.patientID) {
                            return {
                                ...p,
                                ...patient
                            }
                        }
                        return p
                    })
                })
                return;
            }

            if (message.type === 'remove_id') {
                const patient = message.data as PatientData
                setPatient((prev) => {
                    return prev?.filter(p => p.patientID !== patient.patientID)
                })
                return;
            }

            if (message.type === 'update_consultation') {
                const consultation = message.data as PatientConsultation
                setPatient((prev) => {
                    const updatedList = prev?.map(p => {
                        if (p.patientID === consultation.patientID) {
                            return {
                                ...p,
                                consultation: {
                                    ...p.consultation,
                                    ...consultation
                                }
                            }
                        }
                        return p
                    })

                    return updatedList?.sort((a, b) => {
                        if (a.consultation?.meetingOngoing !== b.consultation?.meetingOngoing) {
                            return a.consultation?.meetingOngoing ? -1 : 1;
                        }

                        if (a.consultation?.consultationDate && b.consultation?.consultationDate) {
                            return new Date(a.consultation.consultationDate).getTime() - new Date(b.consultation.consultationDate).getTime();
                        }
                        return 0;
                    });
                })
                return;
            }

            if (message.type === 'update_timer') {
                const timer = message.data as TimeStarter
                if (doctor?.accessID === timer.timerKey || doctor?.role === 'admin') {
                    if (doctor?.role === 'admin') {
                        ApiClient.getDoctorBySupabaseId(timer.timerKey).then((dr) => {
                            setTimerKey((prev) => {
                                return {
                                    ...prev,
                                    [timer.timerKey]: timer.timerKey
                                }
                            })

                            setDrList((prev) => {
                                return {
                                    ...prev,
                                    [timer.timerKey]: dr
                                }

                            }
                            )
                        }
                        )
                    }
                    else {
                        setTimerKey((prev) => {
                            return {
                                ...prev,
                                [doctor.accessID]: timer.timerKey
                            }
                        })

                        setDrList((prev) => {
                            return {
                                ...prev,
                                [doctor.accessID]: doctor
                            }
                        }
                        )
                    }

                }
            }

            if (message.type === 'update_patient_queue') {
                // Handle real-time patient queue status updates
                const queueData = message.data as any; // PatientQueue type
                if (queueData && queueData.patientID) {
                    // Trigger a refresh of patient status for components using real-time status
                    // This will cause components to re-fetch batch status including the updated patient
                    setPatient((prev) => {
                        // Force a re-render by updating the array reference
                        return prev ? [...prev] : prev;
                    });
                }
                return;
            }

            if (message.type === 'clear_timer') {
                const timer = message.data as TimeStarter
                if (doctor?.accessID === timer.timerKey || doctor?.role === 'admin') {
                    if (doctor?.role === 'admin') {
                        ApiClient.getDoctorBySupabaseId(timer.timerKey).then((dr) => {
                            setTimerKey((prev) => {
                                return {
                                    ...prev,
                                    [timer.timerKey]: undefined
                                }
                            })

                            setDrList((prev) => {
                                return {
                                    ...prev,
                                    [timer.timerKey]: dr
                                }

                            }
                            )
                        }
                        )
                    }
                    else {
                        setTimerKey((prev) => {
                            return {
                                ...prev,
                                [doctor.accessID]: undefined
                            }
                        })

                        setDrList((prev) => {
                            return {
                                ...prev,
                                [doctor.accessID]: doctor
                            }

                        }
                        )
                    }

                }
            }

        }

        ws.onclose = () => {
            setRestart(!restart)
        };

        ws.onerror = (error) => {
            console.log('ws error:', error);
        };

    }, [restart])

    const context: PatientContextType = {
        patients,
        setPatient,
        selectedPatient,
        setSelectedPatient,
        patientTreatmentPlan,
        setPatientTreatmentPlan,
        getPatientById,
        setError,
        selectedPlan,
        setSelectedPlan,
        strength,
        setStrength,
        selectedForms,
        setSelectedForms,
        selectedHistory,
        setSelectedHistory,
        waitingTimes,
        setWaitingTimes,
        drId,
        setDrId,
        updateLockStatus,
        notificationInterval,
        setNotificationInterval,
        setSelectedSearchedPatient,
        selectedSearchedPatient,
        searchedpatients,
        setSearchedPatients,
        restartTimerDialog,
        setRestartTimerDialog,
        setTimerKey,
        timerKey,
        drList,
        setAllDoctors,
        allDoctors,
        setDrList,
        setSelectedInboxItem,
        selectedInboxItem,
        setInboxData,
        inboxData,
        setCurrentSearchPage,
        currentSearchPage,
        updateInboxData,
        fetchHistory,
        selectedPatientHistory,
        setPatientHistory,
        patientHistory,
        setSelectedPatientHistory,
        selectedSupplyDocument,
        setSelectedSupplyDocument,
        reloadConsultationForm,
        setReloadConsultationForm,
        // Patient History Dialog State
        isPatientHistoryDialogOpen,
        isPatientHistoryLoading,
        patientHistoryError,
        openPatientHistoryDialog,
        closePatientHistoryDialog
    }

    return <PatientContext.Provider value={context}>
        {error && <>
            <Grid sx={{ mt: 10 }}>
                <ApiErrorMessage error={error} />
            </Grid>
        </>}
        {children}
    </PatientContext.Provider>
}
export const usePatient = () => {
    const context = useContext(PatientContext)
    if (!context) {
        throw new Error("usePatient must be used within a PatientProvider")
    }
    return context
}
